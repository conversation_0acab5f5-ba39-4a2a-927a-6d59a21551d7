{"name": "dokkai-service", "version": "1.0.0", "description": "Dokkai API Service - Cloudflare Worker", "main": "src/index.ts", "scripts": {"dev": "wrangler dev --local --persist-to ./local-data", "dev:remote": "wrangler dev", "build": "tsc && wrangler deploy --dry-run", "deploy:dev": "wrangler deploy --env development", "deploy:staging": "wrangler deploy --env staging", "deploy:prod": "wrangler deploy --env production", "type-check": "tsc --noEmit"}, "dependencies": {}, "devDependencies": {"@cloudflare/workers-types": "^4.0.0", "typescript": "^5.0.0", "wrangler": "^3.0.0"}}