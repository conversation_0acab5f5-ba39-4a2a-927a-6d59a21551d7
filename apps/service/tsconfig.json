{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["@cloudflare/workers-types"], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@shared/*": ["../../shared/*"]}}, "include": ["src/**/*", "../../shared/**/*"], "exclude": ["node_modules", "dist"]}