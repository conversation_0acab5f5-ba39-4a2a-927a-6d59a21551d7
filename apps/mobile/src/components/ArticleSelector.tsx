import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '../styles/theme';
import { ArticleMetadata } from '@shared/types';
import { TagSelectionModal } from './TagSelectionModal';

interface ArticleSelectorProps {
  visible: boolean;
  articles: ArticleMetadata[];
  currentArticleId: string;
  onSelect: (articleId: string) => void;
  onClose: () => void;
  loading?: boolean;
}

const difficultyLabels = {
  beginner: '初級',
  intermediate: '中級',
  advanced: '上級',
};

const difficultyColors = {
  beginner: colors.green[500],
  intermediate: colors.blue[500],
  advanced: colors.purple[500],
};

export const ArticleSelector: React.FC<ArticleSelectorProps> = ({
  visible,
  articles,
  currentArticleId,
  onSelect,
  onClose,
  loading = false,
}) => {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedDifficulties, setSelectedDifficulties] = useState<string[]>([]);
  const [showTagModal, setShowTagModal] = useState(false);

  // Get all unique tags from articles
  const allTags = useMemo(() => {
    const tagSet = new Set<string>();
    articles.forEach(article => {
      article.tags.forEach(tag => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
  }, [articles]);

  // Filter articles based on selected tags and difficulties
  const filteredArticles = useMemo(() => {
    let filtered = articles;
    
    // Filter by difficulties
    if (selectedDifficulties.length > 0) {
      filtered = filtered.filter(article => 
        selectedDifficulties.includes(article.difficulty)
      );
    }
    
    // Filter by tags
    if (selectedTags.length > 0) {
      filtered = filtered.filter(article => 
        selectedTags.some(selectedTag => article.tags.includes(selectedTag))
      );
    }
    
    return filtered;
  }, [articles, selectedTags, selectedDifficulties]);

  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const toggleDifficulty = (difficulty: string) => {
    setSelectedDifficulties(prev => 
      prev.includes(difficulty) 
        ? prev.filter(d => d !== difficulty)
        : [...prev, difficulty]
    );
  };

  const clearAllTags = () => {
    setSelectedTags([]);
  };

  const clearAllFilters = () => {
    setSelectedTags([]);
    setSelectedDifficulties([]);
  };

  // Difficulty options
  const difficulties = [
    { key: 'beginner', label: '初級' },
    { key: 'intermediate', label: '中級' }, 
    { key: 'advanced', label: '上級' }
  ];
  const renderArticleItem = ({ item }: { item: ArticleMetadata }) => {
    const isSelected = item.id === currentArticleId;
    
    return (
      <TouchableOpacity
        style={[
          styles.articleItem,
          isSelected && styles.selectedArticleItem,
        ]}
        onPress={() => onSelect(item.id)}
        disabled={loading}
        activeOpacity={0.7}
        hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
      >
        <View style={styles.articleHeader}>
          <Text style={[
            styles.articleTitle,
            isSelected && styles.selectedText,
          ]}>
            {item.title}
          </Text>
          {isSelected && (
            <Ionicons
              name="checkmark-circle"
              size={20}
              color={colors.blue[500]}
            />
          )}
        </View>
        
        <Text style={[
          styles.articleDescription,
          isSelected && styles.selectedDescription,
        ]}>
          {item.description}
        </Text>
        
        <View style={styles.articleMeta}>
          <View style={[
            styles.difficultyBadge,
            { backgroundColor: difficultyColors[item.difficulty] + '20' }
          ]}>
            <Text style={[
              styles.difficultyText,
              { color: difficultyColors[item.difficulty] }
            ]}>
              {difficultyLabels[item.difficulty]}
            </Text>
          </View>
          
          <Text style={styles.metaText}>
            {item.estimatedReadingTime} 分
          </Text>
          
          <Text style={styles.metaText}>
            {item.sentenceCount} 文
          </Text>
        </View>
        
        {item.tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {item.tags.slice(0, 3).map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
            {item.tags.length > 3 && (
              <Text style={styles.moreArticleTagsText}>+{item.tags.length - 3}</Text>
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>記事を選択</Text>
          <TouchableOpacity
            onPress={onClose}
            style={styles.closeButton}
            disabled={loading}
            activeOpacity={0.7}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons
              name="close"
              size={24}
              color={loading ? colors.slate[400] : colors.slate[600]}
            />
          </TouchableOpacity>
        </View>

        {/* Filter Section */}
        {!loading && (
          <View style={styles.filterSection}>
            <View style={styles.filterHeader}>
              <Text style={styles.filterTitle}>記事を絞り込み</Text>
              {(selectedTags.length > 0 || selectedDifficulties.length > 0) && (
                <TouchableOpacity 
                  onPress={clearAllFilters} 
                  style={styles.clearButton}
                  activeOpacity={0.7}
                  hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
                >
                  <Text style={styles.clearButtonText}>すべて削除</Text>
                </TouchableOpacity>
              )}
            </View>
            
            <View style={styles.filtersContainer}>
              {/* Difficulty Filters */}
              <View style={styles.filterGroup}>
                <Text style={styles.filterGroupTitle}>難易度</Text>
                <View style={styles.filterButtonsRow}>
                  {difficulties.map((difficulty) => {
                    const isSelected = selectedDifficulties.includes(difficulty.key);
                    return (
                      <TouchableOpacity
                        key={difficulty.key}
                        onPress={() => toggleDifficulty(difficulty.key)}
                        style={[
                          styles.difficultyFilterButton,
                          isSelected && styles.selectedDifficultyButton
                        ]}
                        activeOpacity={0.7}
                        hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
                      >
                        <Text style={[
                          styles.difficultyFilterText,
                          isSelected && styles.selectedDifficultyText
                        ]}>
                          {difficulty.label}
                        </Text>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </View>

              {/* Tags Filter Button */}
              {allTags.length > 0 && (
                <View style={styles.filterGroup}>
                  <Text style={styles.filterGroupTitle}>タグ</Text>
                  <TouchableOpacity
                    onPress={() => setShowTagModal(true)}
                    style={styles.tagsFilterButton}
                    activeOpacity={0.7}
                    hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
                  >
                    <Ionicons name="pricetag-outline" size={16} color={colors.slate[600]} />
                    <Text style={styles.tagsFilterText}>
                      {selectedTags.length > 0 ? `${selectedTags.length}個のタグを選択済み` : 'タグを選択'}
                    </Text>
                    <Ionicons name="chevron-forward" size={16} color={colors.slate[400]} />
                  </TouchableOpacity>
                </View>
              )}
            </View>
            
            {/* Filter Results */}
            {(selectedTags.length > 0 || selectedDifficulties.length > 0) && (
              <View style={styles.filterResults}>
                <Text style={styles.filterResultText}>
                  {filteredArticles.length}件の記事が見つかりました
                </Text>
              </View>
            )}
          </View>
        )}

        {/* Content */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.blue[500]} />
            <Text style={styles.loadingText}>記事リストを読み込み中...</Text>
          </View>
        ) : (
          <FlatList
            data={filteredArticles}
            renderItem={renderArticleItem}
            keyExtractor={(item) => item.id}
            style={styles.articleList}
            contentContainerStyle={styles.articleListContent}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            ListEmptyComponent={() => (
              <View style={styles.emptyContainer}>
                <Ionicons name="document-outline" size={48} color={colors.slate[400]} />
                <Text style={styles.emptyText}>一致する記事が見つかりません</Text>
                <TouchableOpacity 
                  onPress={clearAllTags} 
                  style={styles.resetButton}
                  activeOpacity={0.7}
                  hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
                >
                  <Text style={styles.resetButtonText}>フィルターをリセット</Text>
                </TouchableOpacity>
              </View>
            )}
          />
        )}

        {/* Tag Selection Modal */}
        <TagSelectionModal
          visible={showTagModal}
          tags={allTags}
          selectedTags={selectedTags}
          onToggleTag={toggleTag}
          onClearAll={clearAllTags}
          onClose={() => setShowTagModal(false)}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.slate[200],
  },

  headerTitle: {
    ...typography.h2,
    color: colors.slate[900],
  },

  closeButton: {
    padding: spacing.sm,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  loadingText: {
    ...typography.body1,
    color: colors.slate[600],
    marginTop: spacing.md,
  },

  articleList: {
    flex: 1,
  },

  articleListContent: {
    padding: spacing.lg,
  },

  separator: {
    height: spacing.md,
  },

  articleItem: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    borderWidth: 1,
    borderColor: colors.slate[200],
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },

  selectedArticleItem: {
    borderColor: colors.blue[500],
    backgroundColor: colors.blue[50],
  },

  articleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },

  articleTitle: {
    ...typography.h3,
    color: colors.slate[900],
    flex: 1,
    marginRight: spacing.sm,
  },

  selectedText: {
    color: colors.blue[700],
  },

  articleDescription: {
    ...typography.body2,
    color: colors.slate[600],
    marginBottom: spacing.md,
    lineHeight: 20,
  },

  selectedDescription: {
    color: colors.blue[600],
  },

  articleMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },

  difficultyBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    marginRight: spacing.md,
  },

  difficultyText: {
    ...typography.caption,
    fontWeight: '600',
  },

  metaText: {
    ...typography.caption,
    color: colors.slate[500],
    marginRight: spacing.md,
  },

  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },

  tag: {
    backgroundColor: colors.slate[100],
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 8,
    marginRight: spacing.sm,
    marginBottom: spacing.xs,
  },

  tagText: {
    ...typography.caption,
    color: colors.slate[600],
  },

  moreArticleTagsText: {
    ...typography.caption,
    color: colors.slate[500],
    fontStyle: 'italic',
  },

  // Filter section styles
  filterSection: {
    backgroundColor: colors.slate[50],
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.slate[200],
  },

  filtersContainer: {
    paddingHorizontal: spacing.lg,
  },

  filterGroup: {
    marginBottom: spacing.md,
  },

  filterGroupTitle: {
    ...typography.caption,
    fontWeight: '600',
    color: colors.slate[700],
    marginBottom: spacing.xs,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  filterButtonsRow: {
    flexDirection: 'row',
    gap: spacing.sm,
  },

  difficultyFilterButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.slate[300],
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 16,
    minHeight: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },

  selectedDifficultyButton: {
    backgroundColor: colors.blue[500],
    borderColor: colors.blue[500],
  },

  difficultyFilterText: {
    ...typography.caption,
    color: colors.slate[700],
    fontWeight: '500',
  },

  selectedDifficultyText: {
    color: colors.white,
  },

  tagsFilterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.slate[300],
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    minHeight: 40,
    gap: spacing.sm,
  },

  tagsFilterText: {
    ...typography.body2,
    color: colors.slate[700],
    flex: 1,
  },

  filterResults: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.sm,
  },

  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.sm,
  },

  filterTitle: {
    ...typography.body1,
    fontWeight: '600',
    color: colors.slate[900],
  },

  clearButton: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },

  clearButtonText: {
    ...typography.caption,
    color: colors.blue[600],
    fontWeight: '500',
  },

  filterResultText: {
    ...typography.caption,
    color: colors.slate[600],
    textAlign: 'center',
  },

  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xl * 2,
  },

  emptyText: {
    ...typography.body1,
    color: colors.slate[500],
    marginTop: spacing.md,
    marginBottom: spacing.lg,
  },

  resetButton: {
    backgroundColor: colors.blue[500],
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 8,
  },

  resetButtonText: {
    ...typography.body2,
    color: colors.white,
    fontWeight: '500',
  },

});
