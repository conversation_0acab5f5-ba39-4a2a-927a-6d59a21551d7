import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '../styles/theme';

interface TagSelectionModalProps {
  visible: boolean;
  tags: string[];
  selectedTags: string[];
  onToggleTag: (tag: string) => void;
  onClearAll: () => void;
  onClose: () => void;
}

export const TagSelectionModal: React.FC<TagSelectionModalProps> = ({
  visible,
  tags,
  selectedTags,
  onToggleTag,
  onClearAll,
  onClose,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>タグ選択</Text>
          <TouchableOpacity
            onPress={onClose}
            style={styles.closeButton}
            activeOpacity={0.7}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="close" size={24} color={colors.slate[600]} />
          </TouchableOpacity>
        </View>

        {/* Selected Count and Clear Button */}
        <View style={styles.controlsRow}>
          <Text style={styles.selectedCount}>
            {selectedTags.length}個のタグを選択済み
          </Text>
          {selectedTags.length > 0 && (
            <TouchableOpacity
              onPress={onClearAll}
              style={styles.clearAllButton}
              activeOpacity={0.7}
              hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
            >
              <Text style={styles.clearAllText}>すべてクリア</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Tags Grid */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.tagsContainer}
          showsVerticalScrollIndicator={false}
        >
          {tags.map((tag) => {
            const isSelected = selectedTags.includes(tag);
            return (
              <TouchableOpacity
                key={tag}
                onPress={() => onToggleTag(tag)}
                style={[
                  styles.tagButton,
                  isSelected && styles.selectedTagButton
                ]}
                activeOpacity={0.7}
                hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
              >
                <Text style={[
                  styles.tagText,
                  isSelected && styles.selectedTagText
                ]}>
                  {tag}
                </Text>
                {isSelected && (
                  <Ionicons
                    name="checkmark-circle"
                    size={16}
                    color={colors.white}
                    style={styles.checkIcon}
                  />
                )}
              </TouchableOpacity>
            );
          })}
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <TouchableOpacity
            onPress={onClose}
            style={styles.confirmButton}
            activeOpacity={0.7}
          >
            <Text style={styles.confirmButtonText}>
              確定 ({selectedTags.length})
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.slate[200],
  },

  headerTitle: {
    ...typography.h2,
    color: colors.slate[900],
  },

  closeButton: {
    padding: spacing.sm,
  },

  controlsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.slate[50],
  },

  selectedCount: {
    ...typography.body2,
    color: colors.slate[600],
  },

  clearAllButton: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },

  clearAllText: {
    ...typography.caption,
    color: colors.red[500],
    fontWeight: '500',
  },

  scrollView: {
    flex: 1,
  },

  tagsContainer: {
    padding: spacing.lg,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },

  tagButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.slate[300],
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    marginBottom: spacing.sm,
    minHeight: 36,
  },

  selectedTagButton: {
    backgroundColor: colors.blue[500],
    borderColor: colors.blue[500],
  },

  tagText: {
    ...typography.body2,
    color: colors.slate[700],
    fontWeight: '500',
  },

  selectedTagText: {
    color: colors.white,
  },

  checkIcon: {
    marginLeft: spacing.xs,
  },

  footer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.slate[200],
    backgroundColor: colors.white,
  },

  confirmButton: {
    backgroundColor: colors.blue[500],
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
    alignItems: 'center',
    minHeight: 44,
    justifyContent: 'center',
  },

  confirmButtonText: {
    ...typography.body1,
    color: colors.white,
    fontWeight: '600',
  },
});