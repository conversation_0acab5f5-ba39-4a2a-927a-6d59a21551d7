import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  StyleSheet
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { FullTextModalProps } from '@shared/types';
import { colors, spacing, fontSize, borderRadius, globalStyles } from '../styles/theme';

const categoryLabels: Record<string, string> = {
  'daily-life': '日常生活',
  'school-life': '学校生活',
  'travel': '旅行',
  'social-life': '社会生活',
  'culture': '文化',
  'work-life': '職場生活',
};

export const FullTextModal: React.FC<FullTextModalProps> = ({
  visible,
  level,
  data,
  article,
  onClose
}) => {
  if (!level || !data) return null;

  const levelColor = colors.level[level];
  const bgColor = colors.background[level];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: bgColor }]}>
          <View style={styles.headerContent}>
            <View style={styles.titleContainer}>
              <View style={[styles.levelBadge, { backgroundColor: levelColor }]}>
                <Text style={styles.levelText}>{level}</Text>
              </View>
              <View style={styles.titleText}>
                <Text style={[globalStyles.h3, styles.modalTitle]}>
                  {level} レベル完全記事
                </Text>
                <Text style={[globalStyles.caption, styles.description]}>
                  {data.description}
                </Text>
              </View>
            </View>
            
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <Ionicons name="close" size={24} color={colors.slate[400]} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Content */}
        <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            {/* Full Text Section */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Ionicons name="book-outline" size={20} color={colors.slate[600]} />
                <Text style={styles.sectionTitle}>完全記事</Text>
                <Text style={styles.wordCount}>{data.word_count} 文字</Text>
              </View>
              
              <View style={styles.textContainer}>
                <Text style={[styles.fullText, globalStyles.japaneseText]}>
                  {data.full_text}
                </Text>
              </View>
            </View>

            {/* Grammar Points Section */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Ionicons name="school-outline" size={20} color={colors.slate[600]} />
                <Text style={styles.sectionTitle}>文法項目</Text>
              </View>
              
              <View style={styles.grammarPoints}>
                {data.grammar_points.map((point: string, index: number) => (
                  <View key={index} style={styles.grammarPoint}>
                    <Text style={styles.grammarPointText}>{point}</Text>
                  </View>
                ))}
              </View>
            </View>

            {/* Statistics Section */}
            <View style={styles.section}>
              <View style={styles.statsContainer}>
                <Text style={styles.statsTitle}>統計情報</Text>
                <View style={styles.statsGrid}>
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>{data.word_count}</Text>
                    <Text style={styles.statLabel}>文字数</Text>
                  </View>
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>{data.sentences.length}</Text>
                    <Text style={styles.statLabel}>文数</Text>
                  </View>
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>{data.grammar_points.length}</Text>
                    <Text style={styles.statLabel}>文法項目</Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Article Information Section */}
            {article && (
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <Ionicons name="information-circle-outline" size={20} color={colors.slate[600]} />
                  <Text style={styles.sectionTitle}>記事情報</Text>
                </View>
                
                <View style={styles.articleInfo}>
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>カテゴリー</Text>
                    <Text style={styles.infoValue}>
                      {categoryLabels[article.category] || article.category}
                    </Text>
                  </View>
                  
                  {article.tags.length > 0 && (
                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>タグ</Text>
                      <View style={styles.tagsContainer}>
                        {article.tags.map((tag, index) => (
                          <View key={index} style={styles.tag}>
                            <Text style={styles.tagText}>{tag}</Text>
                          </View>
                        ))}
                      </View>
                    </View>
                  )}
                </View>
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white
  },

  header: {
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
    paddingHorizontal: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.slate[200]
  },

  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },

  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1
  },

  levelBadge: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.full,
    marginRight: spacing.md
  },

  levelText: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontWeight: '700'
  },

  titleText: {
    flex: 1
  },

  modalTitle: {
    marginBottom: spacing.xs
  },

  description: {
    color: colors.slate[600]
  },

  closeButton: {
    padding: spacing.sm,
    marginLeft: spacing.md
  },

  scrollContent: {
    flex: 1
  },

  content: {
    padding: spacing.lg,
    paddingBottom: spacing['2xl']
  },

  section: {
    marginBottom: spacing.xl
  },

  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md
  },

  sectionTitle: {
    fontSize: fontSize.lg,
    fontWeight: '600',
    color: colors.slate[700],
    marginLeft: spacing.sm,
    flex: 1
  },

  wordCount: {
    fontSize: fontSize.sm,
    color: colors.slate[500]
  },

  textContainer: {
    backgroundColor: colors.slate[50],
    borderRadius: borderRadius.lg,
    padding: spacing.lg
  },

  fullText: {
    fontSize: fontSize.base,
    lineHeight: 28,
    color: colors.slate[800]
  },

  grammarPoints: {
    gap: spacing.sm
  },

  grammarPoint: {
    backgroundColor: colors.slate[50],
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.slate[200]
  },

  grammarPointText: {
    fontSize: fontSize.sm,
    color: colors.slate[700]
  },

  statsContainer: {
    backgroundColor: colors.slate[100],
    borderRadius: borderRadius.lg,
    padding: spacing.lg
  },

  statsTitle: {
    fontSize: fontSize.base,
    fontWeight: '600',
    color: colors.slate[600],
    marginBottom: spacing.md
  },

  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around'
  },

  statItem: {
    alignItems: 'center'
  },

  statValue: {
    fontSize: fontSize.xl,
    fontWeight: '700',
    color: colors.slate[800]
  },

  statLabel: {
    fontSize: fontSize.sm,
    color: colors.slate[600],
    marginTop: spacing.xs
  },

  articleInfo: {
    backgroundColor: colors.slate[50],
    borderRadius: borderRadius.lg,
    padding: spacing.lg
  },

  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md
  },

  infoLabel: {
    fontSize: fontSize.sm,
    fontWeight: '600',
    color: colors.slate[600],
    width: 80,
    marginRight: spacing.md
  },

  infoValue: {
    fontSize: fontSize.sm,
    color: colors.slate[800],
    flex: 1
  },

  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    flex: 1,
    gap: spacing.xs
  },

  tag: {
    backgroundColor: colors.blue[100],
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderWidth: 1,
    borderColor: colors.blue[200]
  },

  tagText: {
    fontSize: fontSize.xs,
    color: colors.blue[700],
    fontWeight: '500'
  }
});