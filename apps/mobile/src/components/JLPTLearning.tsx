import { StatusBar } from 'expo-status-bar';
import Constants from 'expo-constants';
import React, { useEffect, useState } from 'react';
import { Al<PERSON>, ScrollView, StyleSheet, Text, View } from 'react-native';
import { useArticleData, useArticleLearning } from '../hooks/useArticleData';
import { useApiArticleData, useApiArticleLearning } from '../hooks/useApiArticleData';
import { colors, globalStyles, spacing } from '../styles/theme';
import { Level, LevelData } from '@shared/types';

// Components
import { AppHeader } from './AppHeader';
import { ArticleSelector } from './ArticleSelector';
import { FullTextModal } from './FullTextModal';
import { LevelCard } from './LevelCard';
import { NavigationControls } from './NavigationControls';

const levels: Level[] = ['N5', 'N4', 'N3', 'N2', 'N1'];

export const JLPTLearning: React.FC = () => {
  // 从 Expo Constants 读取环境变量配置
  const DATA_SOURCE_MODE = Constants.expoConfig?.extra?.DATA_SOURCE_MODE || 'local';
  const API_BASE_URL = Constants.expoConfig?.extra?.API_BASE_URL || '';

  // 判断是否使用API模式：需要同时满足模式为api且URL不为空
  const useApiService = DATA_SOURCE_MODE === 'api' && API_BASE_URL.trim() !== '';

  // 在console中显示当前配置
  React.useEffect(() => {
    console.log('🔧 Data Source Configuration:');
    console.log(`   Mode: ${DATA_SOURCE_MODE}`);
    console.log(`   API URL: ${API_BASE_URL || 'Not configured'}`);
    console.log(`   Using API Service: ${useApiService}`);
    console.log('   Source: Expo Constants');
  }, [DATA_SOURCE_MODE, API_BASE_URL, useApiService]);

  // Article management - conditional hook usage
  // 注意：两个hooks都会被调用（React规则），但API hook在本地模式时不会执行网络请求
  const legacyHookResult = useArticleData();
  const apiHookResult = useApiArticleData();

  const articleHook = useApiService ? apiHookResult : legacyHookResult;

  const {
    articles,
    currentArticle,
    loading: articleLoading,
    switchingArticle,
    error: articleError,
    switchToArticle,
    clearError,
  } = articleHook;

  // Learning state - conditional hook usage
  const legacyLearningResult = useArticleLearning(currentArticle);
  const apiLearningResult = useApiArticleLearning(currentArticle);

  const learningHook = useApiService ? apiLearningResult : legacyLearningResult;

  const {
    currentSentence,
    maxSentences,
    nextSentence,
    prevSentence,
    canGoNext,
    canGoPrev,
    getCurrentSentences,
    getCurrentGrammarFeatures,
    saveProgress,
  } = learningHook;

  // UI state
  const [showArticleSelector, setShowArticleSelector] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [modalLevel, setModalLevel] = useState<Level | null>(null);
  const [modalData, setModalData] = useState<LevelData | null>(null);

  const sentences = getCurrentSentences();
  const grammarFeatures = getCurrentGrammarFeatures();

  // Error handling
  useEffect(() => {
    if (articleError) {
      Alert.alert(
        '加载错误',
        articleError,
        [
          { text: '重试', onPress: clearError },
          { text: '确定', style: 'cancel' },
        ]
      );
    }
  }, [articleError, clearError]);

  // Event handlers
  const handleMenuPress = () => {
    setShowArticleSelector(true);
  };

  const handleArticleSelect = async (articleId: string) => {
    setShowArticleSelector(false);
    if (articleId !== currentArticle?.id) {
      await saveProgress(); // Save current progress before switching
      await switchToArticle(articleId);
    }
  };

  const handleLevelSelect = (level: Level) => {
    if (!currentArticle) return;

    const levelData = currentArticle.jlptData[level];
    if (levelData) {
      setModalLevel(level);
      setModalData(levelData);
      setShowModal(true);
    }
  };

  const closeModal = () => {
    setShowModal(false);
    setModalLevel(null);
    setModalData(null);
  };

  const handleLevelCardPress = (level: Level) => {
    handleLevelSelect(level);
  };

  // Loading state
  if (articleLoading && !currentArticle) {
    return (
      <View style={[globalStyles.container, styles.container, styles.loadingContainer]}>
        <StatusBar style="dark" />
        <Text style={styles.loadingText}>加载文章中...</Text>
      </View>
    );
  }

  return (
    <View style={[globalStyles.container, styles.container]}>
      <StatusBar style="dark" />

      {/* Header */}
      <AppHeader
        currentArticle={currentArticle}
        onMenuPress={handleMenuPress}
        onLevelSelect={handleLevelSelect}
        availableLevels={levels}
        loading={switchingArticle}
      />



      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        {/* Level Cards */}
        <View style={styles.levelCards}>
          {levels.map((level) => {
            const sentence = sentences[level];
            const grammarFeature = grammarFeatures[level];

            if (!sentence) return null;

            return (
              <LevelCard
                key={level}
                level={level}
                sentence={sentence}
                grammarFeature={grammarFeature}
                onPress={() => handleLevelCardPress(level)}
              />
            );
          })}
        </View>
      </ScrollView>

      {/* Bottom Navigation Controls */}
      <View style={styles.bottomNavigation}>
        <NavigationControls
          currentIndex={currentSentence}
          totalCount={maxSentences}
          onPrevious={prevSentence}
          onNext={nextSentence}
          canGoBack={canGoPrev}
          canGoForward={canGoNext}
        />
      </View>

      {/* Article Selector Modal */}
      <ArticleSelector
        visible={showArticleSelector}
        articles={articles}
        currentArticleId={currentArticle?.id || ''}
        onSelect={handleArticleSelect}
        onClose={() => setShowArticleSelector(false)}
        loading={switchingArticle}
      />

      {/* Full Text Modal */}
      <FullTextModal
        visible={showModal}
        level={modalLevel}
        data={modalData}
        article={currentArticle ? {
          title: currentArticle.title,
          category: currentArticle.category,
          tags: currentArticle.tags
        } : null}
        onClose={closeModal}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.slate[50],
  },

  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },

  loadingText: {
    fontSize: 16,
    color: colors.slate[600],
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl,
  },

  levelCards: {
    marginBottom: spacing.lg,
  },

  bottomNavigation: {
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.slate[200],
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },


});
