import {
    Article,
    ArticleError,
    ArticleErrorType,
    ArticleIndex,
    ArticleMetadata,
    ArticleProgress,
    ArticleService,
    UserProgress,
    validateArticle,
    validateArticleIndex,
} from '@shared/types';

class ArticleServiceImpl implements ArticleService {
  private articleCache: Map<string, Article> = new Map();
  private indexCache: ArticleIndex | null = null;

  async getArticleIndex(): Promise<ArticleIndex> {
    try {
      if (this.indexCache) {
        return this.indexCache;
      }

      console.log('📁 Local Request: Loading from data/index.json');
      console.log('📊 Mode: Local');

      // In a real app, this would be a network request
      const indexData = require('../data/index.json');

      if (!validateArticleIndex(indexData)) {
        throw this.createError(
          ArticleErrorType.INVALID_DATA,
          'Invalid article index format'
        );
      }

      this.indexCache = indexData;
      return indexData;
    } catch (error) {
      if (error instanceof Error && error.message.includes('Cannot resolve module')) {
        throw this.createError(
          ArticleErrorType.FILE_NOT_FOUND,
          'Article index file not found'
        );
      }
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to load article index: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getArticle(id: string): Promise<Article> {
    try {
      // Check cache first
      if (this.articleCache.has(id)) {
        return this.articleCache.get(id)!;
      }

      console.log(`📁 Local Request: Loading article ${id} from data/articles/`);
      console.log('📊 Mode: Local');

      // Load article from file
      let articleData: any;
      try {
        switch (id) {
          // Original articles
          case 'daily-life-student':
            articleData = require('../data/articles/daily-life-student.json');
            break;
          case 'family-introduction':
            articleData = require('../data/articles/family-introduction.json');
            break;
          case 'weekend-activities':
            articleData = require('../data/articles/weekend-activities.json');
            break;
          case 'shopping-experience':
            articleData = require('../data/articles/shopping-experience.json');
            break;
          case 'part-time-job':
            articleData = require('../data/articles/part-time-job.json');
            break;
          case 'school-activities':
            articleData = require('../data/articles/school-activities.json');
            break;
          case 'travel-adventure':
            articleData = require('../data/articles/travel-adventure.json');
            break;
          case 'job-hunting':
            articleData = require('../data/articles/job-hunting.json');
            break;
          case 'homestay-experience':
            articleData = require('../data/articles/homestay-experience.json');
            break;
          case 'train-travel':
            articleData = require('../data/articles/train-travel.json');
            break;

          // New articles added to index
          case 'asset-management':
            articleData = require('../data/articles/asset-management.json');
            break;
          case 'community-activities':
            articleData = require('../data/articles/community-activities.json');
            break;
          case 'convenience-store':
            articleData = require('../data/articles/convenience-store.json');
            break;
          case 'cultural-festival':
            articleData = require('../data/articles/cultural-festival.json');
            break;
          case 'eco-life':
            articleData = require('../data/articles/eco-life.json');
            break;
          case 'emergency-response':
            articleData = require('../data/articles/emergency-response.json');
            break;
          case 'environmental-actions':
            articleData = require('../data/articles/environmental-actions.json');
            break;
          case 'hair-salon-experience':
            articleData = require('../data/articles/hair-salon-experience.json');
            break;
          case 'health-management':
            articleData = require('../data/articles/health-management.json');
            break;
          case 'hospital-visit':
            articleData = require('../data/articles/hospital-visit.json');
            break;
          case 'insurance-registration':
            articleData = require('../data/articles/insurance-registration.json');
            break;
          case 'japanese-education-system':
            articleData = require('../data/articles/japanese-education-system.json');
            break;
          case 'japanese-traditional-culture':
            articleData = require('../data/articles/japanese-traditional-culture.json');
            break;
          case 'job-hunting-process':
            articleData = require('../data/articles/job-hunting-process.json');
            break;
          case 'mobile-phone-purchase':
            articleData = require('../data/articles/mobile-phone-purchase.json');
            break;
          case 'morning-breakfast':
            articleData = require('../data/articles/morning-breakfast.json');
            break;
          case 'moving-preparation':
            articleData = require('../data/articles/moving-preparation.json');
            break;
          case 'online-shopping':
            articleData = require('../data/articles/online-shopping.json');
            break;
          case 'parenting-life':
            articleData = require('../data/articles/parenting-life.json');
            break;
          case 'park-holiday':
            articleData = require('../data/articles/park-holiday.json');
            break;
          case 'remote-work':
            articleData = require('../data/articles/remote-work.json');
            break;
          case 'rental-contract':
            articleData = require('../data/articles/rental-contract.json');
            break;
          case 'restaurant-dining':
            articleData = require('../data/articles/restaurant-dining.json');
            break;
          case 'seasonal-events':
            articleData = require('../data/articles/seasonal-events.json');
            break;
          case 'train-commute':
            articleData = require('../data/articles/train-commute.json');
            break;
          case 'visa-renewal':
            articleData = require('../data/articles/visa-renewal.json');
            break;

          default:
            throw this.createError(
              ArticleErrorType.FILE_NOT_FOUND,
              `Article with id "${id}" not found`
            );
        }
      } catch (error) {
        throw this.createError(
          ArticleErrorType.FILE_NOT_FOUND,
          `Article file for "${id}" not found`
        );
      }

      if (!validateArticle(articleData)) {
        throw this.createError(
          ArticleErrorType.INVALID_DATA,
          `Invalid article format for "${id}"`
        );
      }

      // Cache the article
      this.articleCache.set(id, articleData);
      return articleData;
    } catch (error) {
      if (error instanceof Error && error.name === 'ArticleError') {
        throw error;
      }
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to load article "${id}": ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getAllArticles(): Promise<Article[]> {
    try {
      const index = await this.getArticleIndex();
      const articles: Article[] = [];

      for (const metadata of index.articles) {
        try {
          const article = await this.getArticle(metadata.id);
          articles.push(article);
        } catch (error) {
          console.warn(`Failed to load article ${metadata.id}:`, error);
          // Continue loading other articles
        }
      }

      return articles;
    } catch (error) {
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to load all articles: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getFilteredArticles(options: {
    tags?: string[];
    categories?: string[];
    difficulties?: string[];
  } = {}): Promise<ArticleMetadata[]> {
    try {
      const index = await this.getArticleIndex();
      let filteredArticles = index.articles;

      // Filter by tags
      if (options.tags && options.tags.length > 0) {
        filteredArticles = filteredArticles.filter(article =>
          options.tags!.some(tag => article.tags.includes(tag))
        );
      }

      // Filter by categories
      if (options.categories && options.categories.length > 0) {
        filteredArticles = filteredArticles.filter(article =>
          options.categories!.includes(article.category)
        );
      }

      // Filter by difficulties
      if (options.difficulties && options.difficulties.length > 0) {
        filteredArticles = filteredArticles.filter(article =>
          options.difficulties!.includes(article.difficulty)
        );
      }

      return filteredArticles;
    } catch (error) {
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to filter articles: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getAllTags(): Promise<string[]> {
    try {
      const index = await this.getArticleIndex();
      const tagSet = new Set<string>();

      index.articles.forEach(article => {
        article.tags.forEach(tag => tagSet.add(tag));
      });

      return Array.from(tagSet).sort();
    } catch (error) {
      throw this.createError(
        ArticleErrorType.DATA_PARSE_ERROR,
        `Failed to get all tags: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async updateProgress(progress: ArticleProgress): Promise<void> {
    try {
      // In a real app, this would save to persistent storage or send to server
      const userProgress = await this.getUserProgress();
      userProgress.articleProgress[progress.articleId] = progress;
      userProgress.currentArticleId = progress.articleId;

      await this.saveUserProgress(userProgress);
    } catch (error) {
      throw this.createError(
        ArticleErrorType.STORAGE_ERROR,
        `Failed to update progress: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getUserProgress(): Promise<UserProgress> {
    try {
      // In a real app, this would load from persistent storage
      // For now, return default progress
      return {
        currentArticleId: 'daily-life-student',
        articleProgress: {},
        preferences: {
          preferredLevels: ['N5', 'N4', 'N3', 'N2', 'N1'],
          autoSave: true,
          showGrammarHints: true,
        },
        statistics: {
          totalTimeSpent: 0,
          articlesCompleted: 0,
          sentencesRead: 0,
          lastActiveDate: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw this.createError(
        ArticleErrorType.STORAGE_ERROR,
        `Failed to load user progress: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async saveUserProgress(progress: UserProgress): Promise<void> {
    try {
      // In a real app, this would save to persistent storage or send to server
      console.log('Saving user progress:', progress);
      // For now, just log the progress
    } catch (error) {
      throw this.createError(
        ArticleErrorType.STORAGE_ERROR,
        `Failed to save user progress: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  // Utility methods
  clearCache(): void {
    this.articleCache.clear();
    this.indexCache = null;
  }

  getCachedArticle(id: string): Article | null {
    return this.articleCache.get(id) || null;
  }

  private createError(type: ArticleErrorType, message: string, details?: any): ArticleError {
    const error = new Error(message) as any;
    error.name = 'ArticleError';
    error.type = type;
    error.details = details;
    error.timestamp = new Date().toISOString();
    return error;
  }
}

// Export singleton instance
export const articleService = new ArticleServiceImpl();

// Export utility functions
export const getArticleById = (id: string) => articleService.getArticle(id);
export const getAllArticles = () => articleService.getAllArticles();
export const getArticleIndex = () => articleService.getArticleIndex();
export const updateArticleProgress = (progress: ArticleProgress) =>
  articleService.updateProgress(progress);
export const getUserProgress = () => articleService.getUserProgress();

// Error handling utilities
export const isArticleError = (error: any): error is ArticleError => {
  return error && error.name === 'ArticleError';
};

export const getErrorMessage = (error: any): string => {
  if (isArticleError(error)) {
    return error.message;
  }
  if (error instanceof Error) {
    return error.message;
  }
  return 'An unknown error occurred';
};
