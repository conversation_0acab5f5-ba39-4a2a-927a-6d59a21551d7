import { JLPTData, Level, Article, ArticleMetadata, generateArticleId, calculateReadingTime } from '@shared/types';

/**
 * Utility functions for data migration and management
 */

/**
 * Convert legacy JLPT data to new Article format
 */
export const convertJLPTDataToArticle = (
  jlptData: JLPTData,
  title: string,
  description: string,
  category: string = 'general',
  difficulty: 'beginner' | 'intermediate' | 'advanced' = 'beginner',
  tags: string[] = []
): Article => {
  const id = generateArticleId(title);
  const now = new Date().toISOString();
  
  return {
    id,
    title,
    description,
    createdAt: now,
    tags,
    category,
    difficulty,
    estimatedReadingTime: calculateReadingTime(jlptData),
    jlptData,
  };
};

/**
 * Validate JLPT data structure
 */
export const validateJLPTDataStructure = (data: any): data is JLPTData => {
  const levels: Level[] = ['N5', 'N4', 'N3', 'N2', 'N1'];
  
  if (!data || typeof data !== 'object') {
    return false;
  }
  
  return levels.every(level => {
    const levelData = data[level];
    if (!levelData || typeof levelData !== 'object') {
      return false;
    }
    
    return (
      Array.isArray(levelData.sentences) &&
      Array.isArray(levelData.grammar_features) &&
      typeof levelData.full_text === 'string' &&
      Array.isArray(levelData.grammar_points) &&
      typeof levelData.word_count === 'number' &&
      levelData.sentences.length === levelData.grammar_features.length
    );
  });
};

/**
 * Get sentence count from JLPT data
 */
export const getSentenceCount = (jlptData: JLPTData): number => {
  return jlptData.N5?.sentences.length || 0;
};

/**
 * Get all unique grammar points from JLPT data
 */
export const getAllGrammarPoints = (jlptData: JLPTData): string[] => {
  const levels: Level[] = ['N5', 'N4', 'N3', 'N2', 'N1'];
  const allPoints = new Set<string>();
  
  levels.forEach(level => {
    const levelData = jlptData[level];
    if (levelData && levelData.grammar_points) {
      levelData.grammar_points.forEach(point => allPoints.add(point));
    }
  });
  
  return Array.from(allPoints);
};

/**
 * Get difficulty level based on grammar complexity
 */
export const inferDifficulty = (jlptData: JLPTData): 'beginner' | 'intermediate' | 'advanced' => {
  const grammarPoints = getAllGrammarPoints(jlptData);
  const complexityScore = grammarPoints.length;
  
  if (complexityScore <= 10) return 'beginner';
  if (complexityScore <= 20) return 'intermediate';
  return 'advanced';
};

/**
 * Extract tags from grammar features
 */
export const extractTagsFromGrammar = (jlptData: JLPTData): string[] => {
  const levels: Level[] = ['N5', 'N4', 'N3', 'N2', 'N1'];
  const tags = new Set<string>();
  
  levels.forEach(level => {
    const levelData = jlptData[level];
    if (levelData && levelData.grammar_features) {
      levelData.grammar_features.forEach(feature => {
        // Extract meaningful tags from grammar features
        if (feature.includes('自己介绍')) tags.add('自我介绍');
        if (feature.includes('日常') || feature.includes('生活')) tags.add('日常生活');
        if (feature.includes('学校') || feature.includes('大学')) tags.add('学校生活');
        if (feature.includes('旅行') || feature.includes('京都')) tags.add('旅行');
        if (feature.includes('敬语') || feature.includes('謙譲語')) tags.add('敬语');
        if (feature.includes('文学') || feature.includes('古典')) tags.add('文学表达');
      });
    }
  });
  
  return Array.from(tags).slice(0, 5); // Limit to 5 tags
};

/**
 * Create article metadata from article
 */
export const createMetadataFromArticle = (article: Article): ArticleMetadata => {
  return {
    id: article.id,
    title: article.title,
    description: article.description,
    author: article.author,
    createdAt: article.createdAt,
    updatedAt: article.updatedAt,
    tags: article.tags,
    category: article.category,
    difficulty: article.difficulty,
    estimatedReadingTime: article.estimatedReadingTime,
    sentenceCount: getSentenceCount(article.jlptData),
  };
};

/**
 * Batch convert multiple JLPT datasets to articles
 */
export const batchConvertToArticles = (
  datasets: Array<{
    jlptData: JLPTData;
    title: string;
    description: string;
    category?: string;
    difficulty?: 'beginner' | 'intermediate' | 'advanced';
    customTags?: string[];
  }>
): Article[] => {
  return datasets.map(dataset => {
    const autoTags = extractTagsFromGrammar(dataset.jlptData);
    const tags = dataset.customTags ? [...dataset.customTags, ...autoTags] : autoTags;
    const difficulty = dataset.difficulty || inferDifficulty(dataset.jlptData);
    
    return convertJLPTDataToArticle(
      dataset.jlptData,
      dataset.title,
      dataset.description,
      dataset.category || 'general',
      difficulty,
      [...new Set(tags)] // Remove duplicates
    );
  });
};

/**
 * Sort articles by difficulty and creation date
 */
export const sortArticles = (articles: Article[]): Article[] => {
  const difficultyOrder = { beginner: 1, intermediate: 2, advanced: 3 };
  
  return [...articles].sort((a, b) => {
    // First sort by difficulty
    const diffA = difficultyOrder[a.difficulty];
    const diffB = difficultyOrder[b.difficulty];
    
    if (diffA !== diffB) {
      return diffA - diffB;
    }
    
    // Then sort by creation date (newest first)
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });
};

/**
 * Filter articles by criteria
 */
export const filterArticles = (
  articles: Article[],
  criteria: {
    difficulty?: 'beginner' | 'intermediate' | 'advanced';
    category?: string;
    tags?: string[];
    minReadingTime?: number;
    maxReadingTime?: number;
  }
): Article[] => {
  return articles.filter(article => {
    if (criteria.difficulty && article.difficulty !== criteria.difficulty) {
      return false;
    }
    
    if (criteria.category && article.category !== criteria.category) {
      return false;
    }
    
    if (criteria.tags && criteria.tags.length > 0) {
      const hasMatchingTag = criteria.tags.some(tag => 
        article.tags.includes(tag)
      );
      if (!hasMatchingTag) {
        return false;
      }
    }
    
    if (criteria.minReadingTime && article.estimatedReadingTime < criteria.minReadingTime) {
      return false;
    }
    
    if (criteria.maxReadingTime && article.estimatedReadingTime > criteria.maxReadingTime) {
      return false;
    }
    
    return true;
  });
};

/**
 * Get reading statistics from articles
 */
export const getReadingStatistics = (articles: Article[]) => {
  const totalArticles = articles.length;
  const totalSentences = articles.reduce((sum, article) => 
    sum + getSentenceCount(article.jlptData), 0
  );
  const totalReadingTime = articles.reduce((sum, article) => 
    sum + article.estimatedReadingTime, 0
  );
  
  const difficultyDistribution = articles.reduce((acc, article) => {
    acc[article.difficulty] = (acc[article.difficulty] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const categoryDistribution = articles.reduce((acc, article) => {
    acc[article.category] = (acc[article.category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  return {
    totalArticles,
    totalSentences,
    totalReadingTime,
    averageReadingTime: totalReadingTime / totalArticles,
    averageSentences: totalSentences / totalArticles,
    difficultyDistribution,
    categoryDistribution,
  };
};
