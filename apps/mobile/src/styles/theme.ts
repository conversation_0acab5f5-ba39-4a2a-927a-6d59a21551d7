import { StyleSheet } from 'react-native';

// 颜色主题
export const colors = {
  primary: '#3b82f6',
  secondary: '#8b5cf6',
  success: '#22c55e',
  warning: '#f59e0b',
  error: '#ef4444',

  // 级别颜色
  level: {
    N5: '#22c55e',
    N4: '#3b82f6',
    N3: '#a855f7',
    N2: '#f97316',
    N1: '#ef4444'
  },

  // 背景颜色
  background: {
    N5: '#f0fdf4',
    N4: '#eff6ff',
    N3: '#faf5ff',
    N2: '#fff7ed',
    N1: '#fef2f2'
  },

  // 中性颜色
  slate: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a'
  },

  // 其他颜色
  green: {
    50: '#f0fdf4',
    500: '#22c55e',
  },

  blue: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
  },

  purple: {
    500: '#a855f7',
  },

  red: {
    500: '#ef4444',
  },

  white: '#ffffff',
  black: '#000000',
  
  // 额外颜色定义
  lightGray: '#f3f4f6',
  gray: '#9ca3af',
};

// 间距
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64
};

// 字体大小
export const fontSize = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36
};

// 圆角
export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999
};

// 阴影
export const shadows = {
  sm: {
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1
  },
  md: {
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3
  },
  lg: {
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 5
  }
};

// 全局样式
export const globalStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.slate[50]
  },

  card: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
    padding: spacing.lg,
    ...shadows.md
  },

  // 文本样式
  h1: {
    fontSize: fontSize['4xl'],
    fontWeight: '700',
    color: colors.slate[800]
  },

  h2: {
    fontSize: fontSize['3xl'],
    fontWeight: '600',
    color: colors.slate[800]
  },

  h3: {
    fontSize: fontSize['2xl'],
    fontWeight: '600',
    color: colors.slate[700]
  },

  body: {
    fontSize: fontSize.base,
    color: colors.slate[700],
    lineHeight: 24
  },

  caption: {
    fontSize: fontSize.sm,
    color: colors.slate[500]
  },

  // 按钮样式
  button: {
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    alignItems: 'center',
    justifyContent: 'center'
  },

  buttonPrimary: {
    backgroundColor: colors.primary
  },

  buttonDisabled: {
    backgroundColor: colors.slate[200]
  },

  buttonText: {
    fontSize: fontSize.base,
    fontWeight: '600',
    color: colors.white
  },

  buttonTextDisabled: {
    color: colors.slate[400]
  },

  // 日语字体
  japaneseText: {
    fontFamily: 'System'  // React Native会自动选择合适的日语字体
  }
});

// 排版样式
export const typography = {
  h1: {
    fontSize: fontSize['4xl'],
    fontWeight: '700' as const,
    color: colors.slate[800],
    lineHeight: fontSize['4xl'] * 1.2,
  },

  h2: {
    fontSize: fontSize['3xl'],
    fontWeight: '600' as const,
    color: colors.slate[800],
    lineHeight: fontSize['3xl'] * 1.2,
  },

  h3: {
    fontSize: fontSize['2xl'],
    fontWeight: '600' as const,
    color: colors.slate[700],
    lineHeight: fontSize['2xl'] * 1.2,
  },

  body1: {
    fontSize: fontSize.base,
    fontWeight: '400' as const,
    color: colors.slate[700],
    lineHeight: fontSize.base * 1.5,
  },

  body2: {
    fontSize: fontSize.sm,
    fontWeight: '400' as const,
    color: colors.slate[600],
    lineHeight: fontSize.sm * 1.5,
  },

  caption: {
    fontSize: fontSize.xs,
    fontWeight: '400' as const,
    color: colors.slate[500],
    lineHeight: fontSize.xs * 1.4,
  },

  button: {
    fontSize: fontSize.base,
    fontWeight: '600' as const,
    lineHeight: fontSize.base * 1.2,
  },
};

// 工具函数
export const getTheme = () => ({
  colors,
  spacing,
  fontSize,
  borderRadius,
  shadows,
  globalStyles
});
