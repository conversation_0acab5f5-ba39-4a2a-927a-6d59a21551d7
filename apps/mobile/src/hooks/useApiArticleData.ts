import Constants from 'expo-constants';
import { useState, useEffect, useCallback } from 'react';
import {
  Article,
  ArticleIndex,
  ArticleMetadata,
  ArticleProgress,
  UserProgress,
  Level,
  LearningProgress,
} from '@shared/types';
import {
  apiArticleService,
  getErrorMessage,
  isArticleError,
} from '../services/apiArticleService';

interface UseApiArticleDataReturn {
  // Article data
  articles: ArticleMetadata[];
  currentArticle: Article | null;
  articleIndex: ArticleIndex | null;

  // Loading states
  loading: boolean;
  articlesLoading: boolean;
  switchingArticle: boolean;

  // Error states
  error: string | null;

  // Actions
  loadArticles: () => Promise<void>;
  switchToArticle: (articleId: string) => Promise<void>;
  refreshCurrentArticle: () => Promise<void>;
  searchArticles: (query: string, options?: any) => Promise<{ articles: ArticleMetadata[]; total: number }>;
  getCategories: () => Promise<string[]>;
  getTags: () => Promise<string[]>;
  clearError: () => void;
}

export const useApiArticleData = (): UseApiArticleDataReturn => {
  // 检查是否应该使用API服务
  const DATA_SOURCE_MODE = Constants.expoConfig?.extra?.DATA_SOURCE_MODE || 'local';
  const API_BASE_URL = Constants.expoConfig?.extra?.API_BASE_URL || '';
  const shouldUseApi = DATA_SOURCE_MODE === 'api' && API_BASE_URL.trim() !== '';

  const [articles, setArticles] = useState<ArticleMetadata[]>([]);
  const [currentArticle, setCurrentArticle] = useState<Article | null>(null);
  const [articleIndex, setArticleIndex] = useState<ArticleIndex | null>(null);

  const [loading, setLoading] = useState(false);
  const [articlesLoading, setArticlesLoading] = useState(false);
  const [switchingArticle, setSwitchingArticle] = useState(false);

  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const loadArticles = useCallback(async () => {
    // 如果不应该使用API，直接返回
    if (!shouldUseApi) {
      console.log('📁 API hook called but not using API service (local mode)');
      return;
    }

    setArticlesLoading(true);
    setError(null);

    try {
      const index = await apiArticleService.getArticleIndex();
      setArticleIndex(index);
      setArticles(index.articles);

      // Load current article if not already loaded
      if (!currentArticle && index.currentArticle) {
        const article = await apiArticleService.getArticle(index.currentArticle);
        setCurrentArticle(article);
      }
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      console.error('Failed to load articles:', err);
    } finally {
      setArticlesLoading(false);
    }
  }, [currentArticle, shouldUseApi]);

  const switchToArticle = useCallback(async (articleId: string) => {
    // 如果不应该使用API，直接返回
    if (!shouldUseApi) {
      return;
    }

    if (currentArticle?.id === articleId) {
      return; // Already on this article
    }

    setSwitchingArticle(true);
    setError(null);

    try {
      const article = await apiArticleService.getArticle(articleId);
      setCurrentArticle(article);

      // Update the index to reflect the new current article
      if (articleIndex) {
        const updatedIndex = {
          ...articleIndex,
          currentArticle: articleId,
        };
        setArticleIndex(updatedIndex);
      }
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      console.error('Failed to switch article:', err);
    } finally {
      setSwitchingArticle(false);
    }
  }, [currentArticle, articleIndex, shouldUseApi]);

  const refreshCurrentArticle = useCallback(async () => {
    // 如果不应该使用API，直接返回
    if (!shouldUseApi || !currentArticle) return;

    setLoading(true);
    setError(null);

    try {
      // Clear cache and reload
      apiArticleService.clearCache();
      const article = await apiArticleService.getArticle(currentArticle.id);
      setCurrentArticle(article);
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      console.error('Failed to refresh article:', err);
    } finally {
      setLoading(false);
    }
  }, [currentArticle, shouldUseApi]);

  const searchArticles = useCallback(async (query: string, options?: any) => {
    // 如果不应该使用API，返回空结果
    if (!shouldUseApi) {
      return { articles: [], total: 0 };
    }

    try {
      return await apiArticleService.searchArticles(query, options);
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      throw err;
    }
  }, [shouldUseApi]);

  const getCategories = useCallback(async () => {
    // 如果不应该使用API，返回空数组
    if (!shouldUseApi) {
      return [];
    }

    try {
      return await apiArticleService.getAllCategories();
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      throw err;
    }
  }, [shouldUseApi]);

  const getTags = useCallback(async () => {
    // 如果不应该使用API，返回空数组
    if (!shouldUseApi) {
      return [];
    }

    try {
      return await apiArticleService.getAllTags();
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      throw err;
    }
  }, [shouldUseApi]);

  // Load articles on mount (only if using API)
  useEffect(() => {
    if (shouldUseApi) {
      loadArticles();
    }
  }, [loadArticles, shouldUseApi]);

  return {
    articles,
    currentArticle,
    articleIndex,
    loading: loading || articlesLoading || switchingArticle,
    articlesLoading,
    switchingArticle,
    error,
    loadArticles,
    switchToArticle,
    refreshCurrentArticle,
    searchArticles,
    getCategories,
    getTags,
    clearError,
  };
};

interface UseApiArticleLearningReturn {
  // Learning state
  currentSentence: number;
  maxSentences: number;
  progress: LearningProgress;

  // Navigation
  nextSentence: () => void;
  prevSentence: () => void;
  goToSentence: (index: number) => void;
  canGoNext: boolean;
  canGoPrev: boolean;

  // Data access
  getCurrentSentences: () => Record<Level, string>;
  getCurrentGrammarFeatures: () => Record<Level, string>;
  getProgressPercentage: () => number;

  // Progress management
  saveProgress: () => Promise<void>;
  loadProgress: (articleId: string) => Promise<void>;

  // API-specific methods
  getLevelData: (level: Level) => Promise<any>;
}

export const useApiArticleLearning = (
  currentArticle: Article | null
): UseApiArticleLearningReturn => {
  // 检查是否应该使用API服务
  const DATA_SOURCE_MODE = Constants.expoConfig?.extra?.DATA_SOURCE_MODE || 'local';
  const API_BASE_URL = Constants.expoConfig?.extra?.API_BASE_URL || '';
  const shouldUseApi = DATA_SOURCE_MODE === 'api' && API_BASE_URL.trim() !== '';

  const [currentSentence, setCurrentSentence] = useState(0);
  const [progress, setProgress] = useState<LearningProgress>({
    currentSentence: 0,
    totalSentences: 0,
    completedSentences: [],
  });

  const maxSentences = currentArticle?.jlptData.N5?.sentences.length || 0;

  // Update progress when article or sentence changes
  useEffect(() => {
    setProgress(prev => ({
      ...prev,
      totalSentences: maxSentences,
      currentSentence,
    }));
  }, [maxSentences, currentSentence]);

  // Reset sentence index when article changes
  useEffect(() => {
    if (currentArticle) {
      setCurrentSentence(0);
      loadProgress(currentArticle.id);
    }
  }, [currentArticle?.id]);

  const nextSentence = useCallback(() => {
    if (currentSentence < maxSentences - 1) {
      setCurrentSentence(prev => {
        const next = prev + 1;
        // Mark current sentence as completed
        setProgress(prevProgress => ({
          ...prevProgress,
          completedSentences: [...new Set([...prevProgress.completedSentences, prev])],
        }));
        return next;
      });
    }
  }, [currentSentence, maxSentences]);

  const prevSentence = useCallback(() => {
    if (currentSentence > 0) {
      setCurrentSentence(prev => prev - 1);
    }
  }, [currentSentence]);

  const goToSentence = useCallback((index: number) => {
    if (index >= 0 && index < maxSentences) {
      setCurrentSentence(index);
    }
  }, [maxSentences]);

  const getCurrentSentences = useCallback((): Record<Level, string> => {
    if (!currentArticle) return {} as Record<Level, string>;

    const levels: Level[] = ['N5', 'N4', 'N3', 'N2', 'N1'];
    const sentences: Record<Level, string> = {} as Record<Level, string>;

    levels.forEach(level => {
      const levelData = currentArticle.jlptData[level];
      if (levelData) {
        sentences[level] = levelData.sentences[currentSentence] || '';
      }
    });

    return sentences;
  }, [currentArticle, currentSentence]);

  const getCurrentGrammarFeatures = useCallback((): Record<Level, string> => {
    if (!currentArticle) return {} as Record<Level, string>;

    const levels: Level[] = ['N5', 'N4', 'N3', 'N2', 'N1'];
    const features: Record<Level, string> = {} as Record<Level, string>;

    levels.forEach(level => {
      const levelData = currentArticle.jlptData[level];
      if (levelData) {
        features[level] = levelData.grammar_features[currentSentence] || '';
      }
    });

    return features;
  }, [currentArticle, currentSentence]);

  const getProgressPercentage = useCallback(() => {
    return maxSentences > 0 ? ((currentSentence + 1) / maxSentences) * 100 : 0;
  }, [currentSentence, maxSentences]);

  const saveProgress = useCallback(async () => {
    if (!currentArticle) return;

    try {
      const articleProgress: ArticleProgress = {
        articleId: currentArticle.id,
        currentSentence,
        completedSentences: progress.completedSentences,
        totalSentences: maxSentences,
        lastAccessTime: new Date().toISOString(),
        timeSpent: 0, // TODO: Implement time tracking
      };

      await apiArticleService.updateProgress(articleProgress);
    } catch (error) {
      console.error('Failed to save progress:', error);
    }
  }, [currentArticle, currentSentence, progress.completedSentences, maxSentences]);

  const loadProgress = useCallback(async (articleId: string) => {
    try {
      const userProgress = await apiArticleService.getUserProgress();
      const articleProgress = userProgress.articleProgress[articleId];

      if (articleProgress) {
        setCurrentSentence(articleProgress.currentSentence);
        setProgress(prev => ({
          ...prev,
          completedSentences: articleProgress.completedSentences,
        }));
      }
    } catch (error) {
      console.error('Failed to load progress:', error);
    }
  }, []);

  const getLevelData = useCallback(async (level: Level) => {
    if (!currentArticle) throw new Error('No current article');

    // 如果不应该使用API，返回当前文章的级别数据
    if (!shouldUseApi) {
      return currentArticle.jlptData[level] || null;
    }

    try {
      return await apiArticleService.getArticleLevelData(currentArticle.id, level);
    } catch (error) {
      console.error(`Failed to get level data for ${level}:`, error);
      throw error;
    }
  }, [currentArticle, shouldUseApi]);

  const canGoNext = currentSentence < maxSentences - 1;
  const canGoPrev = currentSentence > 0;

  return {
    currentSentence,
    maxSentences,
    progress,
    nextSentence,
    prevSentence,
    goToSentence,
    canGoNext,
    canGoPrev,
    getCurrentSentences,
    getCurrentGrammarFeatures,
    getProgressPercentage,
    saveProgress,
    loadProgress,
    getLevelData,
  };
};
