import { useState, useEffect, useCallback } from 'react';
import {
  Article,
  ArticleIndex,
  ArticleMetadata,
  ArticleProgress,
  UserProgress,
  Level,
  LearningProgress,
} from '@shared/types';
import {
  articleService,
  getErrorMessage,
  isArticleError,
} from '../services/articleService';

interface UseArticleDataReturn {
  // Article data
  articles: ArticleMetadata[];
  currentArticle: Article | null;
  articleIndex: ArticleIndex | null;
  
  // Loading states
  loading: boolean;
  articlesLoading: boolean;
  switchingArticle: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  loadArticles: () => Promise<void>;
  switchToArticle: (articleId: string) => Promise<void>;
  refreshCurrentArticle: () => Promise<void>;
  clearError: () => void;
}

export const useArticleData = (): UseArticleDataReturn => {
  const [articles, setArticles] = useState<ArticleMetadata[]>([]);
  const [currentArticle, setCurrentArticle] = useState<Article | null>(null);
  const [articleIndex, setArticleIndex] = useState<ArticleIndex | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [articlesLoading, setArticlesLoading] = useState(false);
  const [switchingArticle, setSwitchingArticle] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const loadArticles = useCallback(async () => {
    setArticlesLoading(true);
    setError(null);
    
    try {
      const index = await articleService.getArticleIndex();
      setArticleIndex(index);
      setArticles(index.articles);
      
      // Load current article if not already loaded
      if (!currentArticle && index.currentArticle) {
        const article = await articleService.getArticle(index.currentArticle);
        setCurrentArticle(article);
      }
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      console.error('Failed to load articles:', err);
    } finally {
      setArticlesLoading(false);
    }
  }, [currentArticle]);

  const switchToArticle = useCallback(async (articleId: string) => {
    if (currentArticle?.id === articleId) {
      return; // Already on this article
    }

    setSwitchingArticle(true);
    setError(null);
    
    try {
      const article = await articleService.getArticle(articleId);
      setCurrentArticle(article);
      
      // Update the index to reflect the new current article
      if (articleIndex) {
        const updatedIndex = {
          ...articleIndex,
          currentArticle: articleId,
        };
        setArticleIndex(updatedIndex);
      }
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      console.error('Failed to switch article:', err);
    } finally {
      setSwitchingArticle(false);
    }
  }, [currentArticle, articleIndex]);

  const refreshCurrentArticle = useCallback(async () => {
    if (!currentArticle) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // Clear cache and reload
      articleService.clearCache();
      const article = await articleService.getArticle(currentArticle.id);
      setCurrentArticle(article);
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      console.error('Failed to refresh article:', err);
    } finally {
      setLoading(false);
    }
  }, [currentArticle]);

  // Load articles on mount
  useEffect(() => {
    loadArticles();
  }, [loadArticles]);

  return {
    articles,
    currentArticle,
    articleIndex,
    loading: loading || articlesLoading || switchingArticle,
    articlesLoading,
    switchingArticle,
    error,
    loadArticles,
    switchToArticle,
    refreshCurrentArticle,
    clearError,
  };
};

interface UseArticleLearningReturn {
  // Learning state
  currentSentence: number;
  maxSentences: number;
  progress: LearningProgress;
  
  // Navigation
  nextSentence: () => void;
  prevSentence: () => void;
  goToSentence: (index: number) => void;
  canGoNext: boolean;
  canGoPrev: boolean;
  
  // Data access
  getCurrentSentences: () => Record<Level, string>;
  getCurrentGrammarFeatures: () => Record<Level, string>;
  getProgressPercentage: () => number;
  
  // Progress management
  saveProgress: () => Promise<void>;
  loadProgress: (articleId: string) => Promise<void>;
}

export const useArticleLearning = (
  currentArticle: Article | null
): UseArticleLearningReturn => {
  const [currentSentence, setCurrentSentence] = useState(0);
  const [progress, setProgress] = useState<LearningProgress>({
    currentSentence: 0,
    totalSentences: 0,
    completedSentences: [],
  });

  const maxSentences = currentArticle?.jlptData.N5?.sentences.length || 0;

  // Update progress when article or sentence changes
  useEffect(() => {
    setProgress(prev => ({
      ...prev,
      totalSentences: maxSentences,
      currentSentence,
    }));
  }, [maxSentences, currentSentence]);

  // Reset sentence index when article changes
  useEffect(() => {
    if (currentArticle) {
      setCurrentSentence(0);
      loadProgress(currentArticle.id);
    }
  }, [currentArticle?.id]);

  const nextSentence = useCallback(() => {
    if (currentSentence < maxSentences - 1) {
      setCurrentSentence(prev => {
        const next = prev + 1;
        // Mark current sentence as completed
        setProgress(prevProgress => ({
          ...prevProgress,
          completedSentences: [...new Set([...prevProgress.completedSentences, prev])],
        }));
        return next;
      });
    }
  }, [currentSentence, maxSentences]);

  const prevSentence = useCallback(() => {
    if (currentSentence > 0) {
      setCurrentSentence(prev => prev - 1);
    }
  }, [currentSentence]);

  const goToSentence = useCallback((index: number) => {
    if (index >= 0 && index < maxSentences) {
      setCurrentSentence(index);
    }
  }, [maxSentences]);

  const getCurrentSentences = useCallback((): Record<Level, string> => {
    if (!currentArticle) return {} as Record<Level, string>;
    
    const levels: Level[] = ['N5', 'N4', 'N3', 'N2', 'N1'];
    const sentences: Record<Level, string> = {} as Record<Level, string>;

    levels.forEach(level => {
      const levelData = currentArticle.jlptData[level];
      if (levelData) {
        sentences[level] = levelData.sentences[currentSentence] || '';
      }
    });

    return sentences;
  }, [currentArticle, currentSentence]);

  const getCurrentGrammarFeatures = useCallback((): Record<Level, string> => {
    if (!currentArticle) return {} as Record<Level, string>;
    
    const levels: Level[] = ['N5', 'N4', 'N3', 'N2', 'N1'];
    const features: Record<Level, string> = {} as Record<Level, string>;

    levels.forEach(level => {
      const levelData = currentArticle.jlptData[level];
      if (levelData) {
        features[level] = levelData.grammar_features[currentSentence] || '';
      }
    });

    return features;
  }, [currentArticle, currentSentence]);

  const getProgressPercentage = useCallback(() => {
    return maxSentences > 0 ? ((currentSentence + 1) / maxSentences) * 100 : 0;
  }, [currentSentence, maxSentences]);

  const saveProgress = useCallback(async () => {
    if (!currentArticle) return;
    
    try {
      const articleProgress: ArticleProgress = {
        articleId: currentArticle.id,
        currentSentence,
        completedSentences: progress.completedSentences,
        totalSentences: maxSentences,
        lastAccessTime: new Date().toISOString(),
        timeSpent: 0, // TODO: Implement time tracking
      };
      
      await articleService.updateProgress(articleProgress);
    } catch (error) {
      console.error('Failed to save progress:', error);
    }
  }, [currentArticle, currentSentence, progress.completedSentences, maxSentences]);

  const loadProgress = useCallback(async (articleId: string) => {
    try {
      const userProgress = await articleService.getUserProgress();
      const articleProgress = userProgress.articleProgress[articleId];
      
      if (articleProgress) {
        setCurrentSentence(articleProgress.currentSentence);
        setProgress(prev => ({
          ...prev,
          completedSentences: articleProgress.completedSentences,
        }));
      }
    } catch (error) {
      console.error('Failed to load progress:', error);
    }
  }, []);

  const canGoNext = currentSentence < maxSentences - 1;
  const canGoPrev = currentSentence > 0;

  return {
    currentSentence,
    maxSentences,
    progress,
    nextSentence,
    prevSentence,
    goToSentence,
    canGoNext,
    canGoPrev,
    getCurrentSentences,
    getCurrentGrammarFeatures,
    getProgressPercentage,
    saveProgress,
    loadProgress,
  };
};
