export type Level = 'N5' | 'N4' | 'N3' | 'N2' | 'N1';

export interface LevelData {
  sentences: string[];
  grammar_features: string[];
  full_text: string;
  grammar_points: string[];
  word_count: number;
  color: string;
  bgColor: string;
  description: string;
}

export interface JLPTData {
  [key: string]: LevelData;
}

export interface LearningProgress {
  currentSentence: number;
  totalSentences: number;
  completedSentences: number[];
}

export interface LevelCardProps {
  level: Level;
  sentence: string;
  grammarFeature: string;
  isActive?: boolean;
  onPress?: () => void;
}

export interface ProgressBarProps {
  current: number;
  total: number;
  style?: any;
}

export interface NavigationControlsProps {
  currentIndex: number;
  totalCount: number;
  onPrevious: () => void;
  onNext: () => void;
  canGoBack: boolean;
  canGoForward: boolean;
}

export interface FullTextModalProps {
  visible: boolean;
  level: Level | null;
  data: LevelData | null;
  article?: {
    title: string;
    category: string;
    tags: string[];
  } | null;
  onClose: () => void;
}