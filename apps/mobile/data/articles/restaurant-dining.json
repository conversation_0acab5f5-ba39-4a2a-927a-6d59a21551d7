{"id": "restaurant-dining", "title": "レストランでの食事", "description": "描述在日本餐厅用餐的场景，包括点餐、用餐和结账等过程。通过不同JLPT级别的表达方式，学习如何用不同复杂程度的日语来描述餐厅用餐的经历。", "author": "Dokkai Team", "createdAt": "2025-07-22T00:00:00.000Z", "tags": ["餐厅", "用餐", "点餐", "日本料理", "社交活动"], "category": "daily-life", "difficulty": "beginner", "estimatedReadingTime": 4, "jlptData": {"N5": {"sentences": ["今日、友達とレストランに行きます。", "レストランに入ります。「こんにちは」と言います。", "店員さんが「何名様ですか」と聞きます。", "「二人です」と答えます。席に案内されます。", "メニューを見ます。「ラーメンをください」と注文します。", "友達は「カレーライスをください」と言います。", "食べ物が来ます。「いただきます」と言います。", "とても美味しいです。「美味しいですね」と話します。", "食べ終わります。「ごちそうさま」と言います。", "お会計をします。千円を払います。「ありがとうございました」。"], "grammar_features": ["と一起", "引用表达", "提问表达", "回答+被动", "请求表达", "引用表达", "自然现象+礼貌用语", "感想表达", "结束表达", "感谢表达"], "full_text": "今日、友達とレストランに行きます。レストランに入ります。「こんにちは」と言います。店員さんが「何名様ですか」と聞きます。「二人です」と答えます。席に案内されます。メニューを見ます。「ラーメンをください」と注文します。友達は「カレーライスをください」と言います。食べ物が来ます。「いただきます」と言います。とても美味しいです。「美味しいですね」と話します。食べ終わります。「ごちそうさま」と言います。お会計をします。千円を払います。「ありがとうございました」。", "grammar_points": ["と（一緒に）", "と言う", "と聞く", "と答える", "に案内される（受身形）", "をください", "と注文する", "と話す"], "word_count": 95, "color": "#22c55e", "bgColor": "#f0fdf4", "description": "入门级：简单句式，基础语法"}, "N4": {"sentences": ["今日は友達と新しくオープンしたレストランに行くことにしました。", "レストランに入ると、店員さんが笑顔で「いらっしゃいませ」と迎えてくれました。", "店員さんに「何名様でしょうか」と聞かれたので、「二人です」と答えました。", "窓側の席に案内されてから、メニューを渡されました。", "メニューを見ながら、友達と何を食べるか相談しました。", "私は「ラーメンをお願いします」と注文し、友達は「カレーライスにします」と言いました。", "しばらく待っていると、美味しそうな料理が運ばれてきました。", "「いただきます」と言ってから、二人で楽しく食事をしました。", "食べ終わった後、「ごちそうさまでした」と言って、お会計をお願いしました。", "店員さんに「またのお越しをお待ちしております」と言われながら、レストランを出ました。"], "grammar_features": ["ことにしました决定", "と条件+てくれました恩惠", "られたので被动+原因", "てから顺序", "ながら同时", "と注文する引用", "ていると时间经过", "てから顺序", "た後顺序", "られながら被动+同时"], "full_text": "今日は友達と新しくオープンしたレストランに行くことにしました。レストランに入ると、店員さんが笑顔で「いらっしゃいませ」と迎えてくれました。店員さんに「何名様でしょうか」と聞かれたので、「二人です」と答えました。窓側の席に案内されてから、メニューを渡されました。メニューを見ながら、友達と何を食べるか相談しました。私は「ラーメンをお願いします」と注文し、友達は「カレーライスにします」と言いました。しばらく待っていると、美味しそうな料理が運ばれてきました。「いただきます」と言ってから、二人で楽しく食事をしました。食べ終わった後、「ごちそうさまでした」と言って、お会計をお願いしました。店員さんに「またのお越しをお待ちしております」と言われながら、レストランを出ました。", "grammar_points": ["ことにする", "と（条件）", "てくれる", "られる（受身形）", "ので", "てから", "ながら", "と注文する", "にする", "ていると", "そうな", "てくる", "た後"], "word_count": 170, "color": "#3b82f6", "bgColor": "#eff6ff", "description": "初级：连接句式，基础复合表达"}, "N3": {"sentences": ["本日、友人と共に最近開店した評判のレストランを訪れることにいたしました。", "店内に足を踏み入れると、店員が笑顔で「いらっしゃいませ」と丁寧に迎えてくださいました。", "「何名様でいらっしゃいますか」と尋ねられましたので、「二名です」と返答いたしました。", "窓際の席へと案内された後、メニューを手渡されました。", "メニューを吟味しながら、友人と何を注文するか検討いたしました。", "私は「特製ラーメンをお願いいたします」と注文し、友人は「スパイシーカレーにいたします」と伝えました。", "しばらく歓談していると、香り高い料理が運ばれてまいりました。", "「いただきます」と述べてから、二人で和やかに食事を楽しみました。", "食事を終えた後、「ごちそうさまでした」と言い、会計をお願いいたしました。", "店員から「またのご来店を心よりお待ちしております」と言われながら、店を後にしました。"], "grammar_features": ["と共に正式", "ことにいたしました敬语", "と丁寧に副词", "られましたので被动+原因", "た後顺序", "しながら同时", "ていると时间经过", "てから顺序", "た後顺序", "られながら被动+同时"], "full_text": "本日、友人と共に最近開店した評判のレストランを訪れることにいたしました。店内に足を踏み入れると、店員が笑顔で「いらっしゃいませ」と丁寧に迎えてくださいました。「何名様でいらっしゃいますか」と尋ねられましたので、「二名です」と返答いたしました。窓際の席へと案内された後、メニューを手渡されました。メニューを吟味しながら、友人と何を注文するか検討いたしました。私は「特製ラーメンをお願いいたします」と注文し、友人は「スパイシーカレーにいたします」と伝えました。しばらく歓談していると、香り高い料理が運ばれてまいりました。「いただきます」と述べてから、二人で和やかに食事を楽しみました。食事を終えた後、「ごちそうさまでした」と言い、会計をお願いいたしました。店員から「またのご来店を心よりお待ちしております」と言われながら、店を後にしました。", "grammar_points": ["と共に", "ことにする（敬体）", "と（条件）", "てくださる", "られる（受身形）", "ので", "た後", "へと", "ながら", "と注文する", "にする", "ていると", "てまいる", "てから", "を後にする"], "word_count": 210, "color": "#a855f7", "bgColor": "#faf5ff", "description": "中级：敬语入门，复杂语法结构"}, "N2": {"sentences": ["本日、友人と共に近頃開業いたしました評判の良いレストランを訪問する運びとなりました。", "店舗内に入店いたしますと、店員が満面の笑みを浮かべながら「いらっしゃいませ」と丁重にお迎えくださいました。", "「何名様でいらっしゃいますでしょうか」とお尋ねいただきましたので、「二名でございます」と回答いたしました。", "窓際の席へとご案内いただいた後、メニューを拝受いたしました。", "メニューを熟読しながら、友人と何を注文するかについて協議いたしました。", "私は「特製ラーメンを頂戴いたします」と注文し、友人は「スパイシーカレーにいたします」と申し上げました。", "しばらく歓談いたしておりますと、香り高い料理が運ばれて参りました。", "「いただきます」と述べてから、二人で和やかな雰囲気の中、食事を堪能いたしました。", "食事を終えました後、「ごちそうさまでございました」と申し上げ、会計をお願い申し上げました。", "店員より「またのご来店を心よりお待ち申し上げております」と言われながら、店を後にいたしました。"], "grammar_features": ["と共に正式", "運びとなりました敬语", "ますと敬体条件", "いただきましたので敬语原因", "た後顺序", "しながら同时", "ておりますと敬语条件", "てから顺序", "ました後敬语顺序", "られながら被动+同时"], "full_text": "本日、友人と共に近頃開業いたしました評判の良いレストランを訪問する運びとなりました。店舗内に入店いたしますと、店員が満面の笑みを浮かべながら「いらっしゃいませ」と丁重にお迎えくださいました。「何名様でいらっしゃいますでしょうか」とお尋ねいただきましたので、「二名でございます」と回答いたしました。窓際の席へとご案内いただいた後、メニューを拝受いたしました。メニューを熟読しながら、友人と何を注文するかについて協議いたしました。私は「特製ラーメンを頂戴いたします」と注文し、友人は「スパイシーカレーにいたします」と申し上げました。しばらく歓談いたしておりますと、香り高い料理が運ばれて参りました。「いただきます」と述べてから、二人で和やかな雰囲気の中、食事を堪能いたしました。食事を終えました後、「ごちそうさまでございました」と申し上げ、会計をお願い申し上げました。店員より「またのご来店を心よりお待ち申し上げております」と言われながら、店を後にいたしました。", "grammar_points": ["と共に", "運びとなる", "ますと", "ながら", "てくださる", "いただく", "ので", "でございます", "た後", "へと", "について", "ておりますと", "て参る", "てから", "の中", "ました後", "より"], "word_count": 260, "color": "#f97316", "bgColor": "#fff7ed", "description": "中高级：高级敬语，正式文体"}, "N1": {"sentences": ["本日、友人と相携えて、近頃開業の栄を賜りました評判高きレストランを訪問する運びとなりました。", "店舗内に足を踏み入れますと、店員が満面の笑みを湛えながら「いらっしゃいませ」と恭しくお迎え申し上げてくださいました。", "「何名様にてお越しでいらっしゃいますでしょうか」とお尋ね賜りましたゆえ、「二名にて参上いたしております」と恭しく返答申し上げました。", "窓際の席へとご案内を賜りました後、メニューを拝受いたしました。", "メニューを熟覧しつつ、友人と何を注文するかについて協議を重ねました。", "私は「特製ラーメンを頂戴仕りたく存じます」と注文し、友人は「スパイシーカレーを所望いたします」と申し述べました。", "しばらく歓談いたしておりますと、芳醇なる香りを放つ料理が運ばれて参りました。", "「いただきます」と述べてより、二人で和やかなる雰囲気の中、食事を堪能いたしました。", "食事を終えました後、「ごちそうさまでございました」と申し上げ、会計をお願い申し上げました。", "店員より「またのご来臨を心より謹んでお待ち申し上げております」と言われながら、店を後にした次第でございます。"], "grammar_features": ["と相携えて文语", "の栄を賜りました文语", "ますと敬体条件", "を湛えながら文语", "ましたゆえ文语原因", "にて参上いたしております文语", "を賜りました後文语顺序", "しつつ文语同时", "仕りたく存じます文语愿望", "次第でございます文语"], "full_text": "本日、友人と相携えて、近頃開業の栄を賜りました評判高きレストランを訪問する運びとなりました。店舗内に足を踏み入れますと、店員が満面の笑みを湛えながら「いらっしゃいませ」と恭しくお迎え申し上げてくださいました。「何名様にてお越しでいらっしゃいますでしょうか」とお尋ね賜りましたゆえ、「二名にて参上いたしております」と恭しく返答申し上げました。窓際の席へとご案内を賜りました後、メニューを拝受いたしました。メニューを熟覧しつつ、友人と何を注文するかについて協議を重ねました。私は「特製ラーメンを頂戴仕りたく存じます」と注文し、友人は「スパイシーカレーを所望いたします」と申し述べました。しばらく歓談いたしておりますと、芳醇なる香りを放つ料理が運ばれて参りました。「いただきます」と述べてより、二人で和やかなる雰囲気の中、食事を堪能いたしました。食事を終えました後、「ごちそうさまでございました」と申し上げ、会計をお願い申し上げました。店員より「またのご来臨を心より謹んでお待ち申し上げております」と言われながら、店を後にした次第でございます。", "grammar_points": ["と相携えて", "の栄を賜る", "高き", "運びとなる", "ますと", "を湛える", "ながら", "ましたゆえ", "にて参上する", "を賜る", "た後", "しつつ", "について", "仕りたく存じます", "を所望する", "ておりますと", "なる（連体形）", "てより", "の中", "ました後", "次第でございます"], "word_count": 320, "color": "#ef4444", "bgColor": "#fef2f2", "description": "高级：文学表达，复杂修辞"}}}