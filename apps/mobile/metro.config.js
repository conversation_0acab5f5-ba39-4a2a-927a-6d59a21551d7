// metro.config.js
const { getDefaultConfig } = require("expo/metro-config");
const path = require("path");

const config = getDefaultConfig(__dirname);

// 确保手势处理器相关模块正确解析
config.resolver.platforms = ["ios", "android", "native", "web"];

// 配置路径别名
config.resolver.alias = {
  "@shared": path.resolve(__dirname, "../../shared"),
  "@": path.resolve(__dirname, "./src"),
};

// 配置 watchFolders 以监听 shared 目录的变化
config.watchFolders = [
  path.resolve(__dirname, "../../shared"),
];

module.exports = config;
