// 读取环境变量
require('dotenv').config();

export default {
  expo: {
    name: "読解 - 日本語学習アプリ",
    slug: "dokkai",
    owner: "interjc",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "dokkai",
    userInterfaceStyle: "automatic",
    newArchEnabled: true,
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.interjc.dokkai",
      companyName: "YOKO, K.K.",
      config: {
        usesNonExemptEncryption: false
      }
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#ffffff"
      },
      edgeToEdgeEnabled: true
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/favicon.png",
      lang: "ja",
      name: "読解 - 日本語学習アプリ",
      shortName: "DOKKAI",
      description: "JLPT対応の日本語学習アプリ。N5からN1まで段階的に同じ内容を比較学習できます。",
      themeColor: "#ffffff",
      backgroundColor: "#f8fafc"
    },
    plugins: [
      "expo-router",
      [
        "expo-splash-screen",
        {
          image: "./assets/images/splash-icon.png",
          imageWidth: 200,
          resizeMode: "contain",
          backgroundColor: "#ffffff"
        }
      ]
    ],
    experiments: {
      typedRoutes: true
    },
    extra: {
      eas: {
        projectId: "1c8ce928-31e7-40e9-b2d3-273e867521fb"
      },
      // 环境变量配置
      DATA_SOURCE_MODE: process.env.DATA_SOURCE_MODE || 'local',
      API_BASE_URL: process.env.API_BASE_URL || '',
    }
  }
};
