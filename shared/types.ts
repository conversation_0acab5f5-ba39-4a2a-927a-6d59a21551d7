// API 契约类型定义
export type Level = 'N5' | 'N4' | 'N3' | 'N2' | 'N1';

export interface Article {
  id: string;
  title: string;
  description: string;
  author?: string;
  createdAt: string;
  updatedAt?: string;
  tags: string[];
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadingTime: number;
  jlptData: JLPTData;
}

export interface JLPTData {
  N5: LevelData;
  N4: LevelData;
  N3: LevelData;
  N2: LevelData;
  N1: LevelData;
}

export interface LevelData {
  sentences: string[];
  grammar_features: string[];
  full_text: string;
  grammar_points: string[];
  word_count: number;
  description: string;
  color: string;
  bgColor: string;
  audio_metadata?: AudioMetadata[];
}

export interface AudioMetadata {
  sentence_index: number;
  audio_path: string;
  duration: number;
  file_size: number;
  word_timings: WordTiming[];
}

export interface WordTiming {
  word: string;
  start_time: number;
  end_time: number;
  reading?: string;
  part_of_speech?: string;
  definition?: string;
}

// API 响应类型
export interface ArticleListResponse {
  articles: Article[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

export interface AudioResponse {
  audio_url: string;
  timing_url?: string;
  duration: number;
  file_size: number;
  expires_at: string;
}

// UI Component Types
export interface LearningProgress {
  currentSentence: number;
  totalSentences: number;
  completedSentences: number[];
}

export interface LevelCardProps {
  level: Level;
  sentence: string;
  grammarFeature: string;
  isActive?: boolean;
  onPress?: () => void;
}

export interface ProgressBarProps {
  current: number;
  total: number;
  style?: any;
}

export interface NavigationControlsProps {
  currentIndex: number;
  totalCount: number;
  onPrevious: () => void;
  onNext: () => void;
  canGoBack: boolean;
  canGoForward: boolean;
}

export interface FullTextModalProps {
  visible: boolean;
  level: Level | null;
  data: LevelData | null;
  article?: {
    title: string;
    category: string;
    tags: string[];
  } | null;
  onClose: () => void;
}

// Extended Article types for mobile
export interface ArticleMetadata {
  id: string;
  title: string;
  description: string;
  author?: string;
  createdAt: string;
  updatedAt?: string;
  tags: string[];
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadingTime: number;
  sentenceCount: number;
}

export interface ArticleIndex {
  version: string;
  lastUpdated: string;
  currentArticle: string;
  articles: ArticleMetadata[];
}

export interface ArticleProgress {
  articleId: string;
  currentSentence: number;
  completedSentences: number[];
  totalSentences: number;
  lastAccessTime: string;
  timeSpent: number; // in seconds
}

export interface UserProgress {
  currentArticleId: string;
  articleProgress: Record<string, ArticleProgress>;
  preferences: {
    preferredLevels: Level[];
    autoSave: boolean;
    showGrammarHints: boolean;
  };
  statistics: {
    totalTimeSpent: number;
    articlesCompleted: number;
    sentencesRead: number;
    lastActiveDate: string;
  };
}

// Utility functions
export const createArticleMetadata = (article: Article): ArticleMetadata => {
  return {
    id: article.id,
    title: article.title,
    description: article.description,
    author: article.author,
    createdAt: article.createdAt,
    updatedAt: article.updatedAt,
    tags: article.tags,
    category: article.category,
    difficulty: article.difficulty,
    estimatedReadingTime: article.estimatedReadingTime,
    sentenceCount: article.jlptData.N5?.sentences.length || 0
  };
};

export const calculateReadingTime = (jlptData: JLPTData): number => {
  // Estimate reading time based on sentence count and complexity
  const sentenceCount = jlptData.N5?.sentences.length || 0;
  const baseTime = sentenceCount * 0.5; // 30 seconds per sentence
  return Math.ceil(baseTime / 60); // Convert to minutes
};

export const generateArticleId = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .substring(0, 50);
};

// Service interface
export interface ArticleService {
  getArticleIndex(): Promise<ArticleIndex>;
  getArticle(id: string): Promise<Article>;
  getAllArticles(): Promise<Article[]>;
  getFilteredArticles(options?: {
    tags?: string[];
    categories?: string[];
    difficulties?: string[];
  }): Promise<ArticleMetadata[]>;
  getAllTags(): Promise<string[]>;
  updateProgress(progress: ArticleProgress): Promise<void>;
  getUserProgress(): Promise<UserProgress>;
  saveUserProgress(progress: UserProgress): Promise<void>;
}

// Error types for better error handling
export enum ArticleErrorType {
  NETWORK_ERROR = 'network_error',
  DATA_PARSE_ERROR = 'data_parse_error',
  FILE_NOT_FOUND = 'file_not_found',
  INVALID_DATA = 'invalid_data',
  STORAGE_ERROR = 'storage_error'
}

export interface ArticleError {
  type: ArticleErrorType;
  message: string;
  details?: any;
  timestamp: string;
}

// Validation functions
export const validateArticle = (data: any): data is Article => {
  return (
    typeof data.id === 'string' &&
    typeof data.title === 'string' &&
    typeof data.description === 'string' &&
    typeof data.createdAt === 'string' &&
    Array.isArray(data.tags) &&
    typeof data.category === 'string' &&
    ['beginner', 'intermediate', 'advanced'].includes(data.difficulty) &&
    typeof data.estimatedReadingTime === 'number' &&
    data.jlptData &&
    validateJLPTData(data.jlptData)
  );
};

export const validateArticleIndex = (data: any): data is ArticleIndex => {
  return (
    typeof data.version === 'string' &&
    typeof data.lastUpdated === 'string' &&
    typeof data.currentArticle === 'string' &&
    Array.isArray(data.articles) &&
    data.articles.every((article: any) => validateArticleMetadata(article))
  );
};

export const validateArticleMetadata = (data: any): data is ArticleMetadata => {
  return (
    typeof data.id === 'string' &&
    typeof data.title === 'string' &&
    typeof data.description === 'string' &&
    typeof data.createdAt === 'string' &&
    Array.isArray(data.tags) &&
    typeof data.category === 'string' &&
    ['beginner', 'intermediate', 'advanced'].includes(data.difficulty) &&
    typeof data.estimatedReadingTime === 'number' &&
    typeof data.sentenceCount === 'number'
  );
};

// Helper function to validate JLPT data
const validateJLPTData = (data: any): boolean => {
  const levels: Level[] = ['N5', 'N4', 'N3', 'N2', 'N1'];

  return levels.every(level => {
    const levelData = data[level];
    return (
      levelData &&
      Array.isArray(levelData.sentences) &&
      Array.isArray(levelData.grammar_features) &&
      typeof levelData.full_text === 'string' &&
      Array.isArray(levelData.grammar_points) &&
      typeof levelData.word_count === 'number' &&
      typeof levelData.color === 'string' &&
      typeof levelData.bgColor === 'string' &&
      typeof levelData.description === 'string'
    );
  });
};
