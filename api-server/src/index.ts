import compression from 'compression';
import cors from 'cors';
import express from 'express';
import { articlesRouter } from './routes/articles';
import { metadataRouter } from './routes/metadata';
import { searchRouter } from './routes/search';
import { errorHandler } from './utils/errors';

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:19006',
    'exp://localhost:19000',
    'http://localhost:8081',
    'https://dokkai.jlpt.me',
    'https://jlpt.me',
    'https://dokkai.com',
    'https://www.dokkai.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

app.use(compression());
app.use(express.json());

// 请求日志中间件
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} - ${req.method} ${req.path} - ${req.ip}`);

  // 添加响应时间统计
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${timestamp} - ${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`);
  });

  next();
});

// API 路由
app.use('/api/articles', articlesRouter);
app.use('/api/search', searchRouter);
app.use('/api', metadataRouter);

// 根路径 - API 信息
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Dokkai Mock API is running',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      articles: '/api/articles',
      search: '/api/search',
      categories: '/api/categories',
      tags: '/api/tags',
      stats: '/api/stats',
      health: '/health',
    },
    documentation: 'See /Users/<USER>/dev/dokkai/documents/api/ for API documentation',
  });
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV || 'development',
  });
});

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    code: 'NOT_FOUND',
    availableEndpoints: [
      'GET /',
      'GET /health',
      'GET /api/articles',
      'GET /api/articles/:id',
      'GET /api/articles/:id/levels/:level',
      'GET /api/search',
      'GET /api/categories',
      'GET /api/tags',
      'GET /api/stats',
    ],
  });
});

// 全局错误处理
app.use(errorHandler);

// 启动服务器
app.listen(PORT, () => {
  console.log('='.repeat(50));
  console.log('🚀 Dokkai Mock API Server Started');
  console.log('='.repeat(50));
  console.log(`📍 Server running on: http://localhost:${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`📚 API base URL: http://localhost:${PORT}/api`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
  console.log('='.repeat(50));

  // 显示主要端点
  console.log('📋 Available endpoints:');
  console.log('  GET  /api/articles           - List all articles');
  console.log('  GET  /api/articles/:id       - Get article details');
  console.log('  GET  /api/articles/:id/levels/:level - Get level data');
  console.log('  GET  /api/search?q=...       - Search articles');
  console.log('  GET  /api/categories         - Get categories');
  console.log('  GET  /api/tags               - Get tags');
  console.log('  GET  /api/stats              - Get statistics');
  console.log('='.repeat(50));
});

// 优雅关闭处理
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM. Shutting down gracefully...');
  process.exit(0);
});
