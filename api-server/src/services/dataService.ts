import fs from 'fs';
import path from 'path';
import { ArticleMetadata, Article, ArticleIndex } from '../types/api';

export class DataService {
  private dataPath: string;
  private indexCache: ArticleIndex | null = null;
  private articleCache: Map<string, Article> = new Map();

  constructor() {
    // 数据文件位于项目根目录的 data 文件夹
    this.dataPath = path.join(__dirname, '..', '..', '..', 'data');
    console.log('Data path:', this.dataPath);
  }

  // 获取文章索引（模拟 D1 数据库）
  async getArticleIndex(): Promise<ArticleIndex> {
    if (this.indexCache) {
      return this.indexCache;
    }

    try {
      const indexPath = path.join(this.dataPath, 'index.json');
      
      if (!fs.existsSync(indexPath)) {
        throw new Error(`Index file not found at: ${indexPath}`);
      }

      const indexData = JSON.parse(fs.readFileSync(indexPath, 'utf-8'));
      this.indexCache = indexData;
      return indexData;
    } catch (error) {
      console.error('Error reading index.json:', error);
      throw new Error('Failed to load article index');
    }
  }

  // 获取文章内容（模拟 R2 存储）
  async getArticleContent(articleId: string): Promise<Article | null> {
    // 检查缓存
    if (this.articleCache.has(articleId)) {
      return this.articleCache.get(articleId)!;
    }

    try {
      const articlePath = path.join(this.dataPath, 'articles', `${articleId}.json`);
      
      if (!fs.existsSync(articlePath)) {
        console.warn(`Article file not found: ${articlePath}`);
        return null;
      }

      const articleData = JSON.parse(fs.readFileSync(articlePath, 'utf-8'));
      
      // 缓存文章内容
      this.articleCache.set(articleId, articleData);
      
      return articleData;
    } catch (error) {
      console.error(`Error reading article ${articleId}:`, error);
      return null;
    }
  }

  // 获取文章列表（支持筛选）
  async getArticles(options: {
    category?: string;
    difficulty?: string;
    tags?: string[];
    limit?: number;
    offset?: number;
  } = {}): Promise<{ articles: ArticleMetadata[]; total: number }> {
    const index = await this.getArticleIndex();
    let filteredArticles = [...index.articles];

    // 应用筛选条件
    if (options.category) {
      filteredArticles = filteredArticles.filter(
        article => article.category === options.category
      );
    }

    if (options.difficulty) {
      filteredArticles = filteredArticles.filter(
        article => article.difficulty === options.difficulty
      );
    }

    if (options.tags && options.tags.length > 0) {
      filteredArticles = filteredArticles.filter(article =>
        options.tags!.some(tag => article.tags.includes(tag))
      );
    }

    const total = filteredArticles.length;

    // 应用分页
    const offset = options.offset || 0;
    const limit = options.limit || 50;
    const paginatedArticles = filteredArticles.slice(offset, offset + limit);

    return {
      articles: paginatedArticles,
      total,
    };
  }

  // 获取单个文章元数据
  async getArticleMetadata(articleId: string): Promise<ArticleMetadata | null> {
    const index = await this.getArticleIndex();
    return index.articles.find(article => article.id === articleId) || null;
  }

  // 搜索文章
  async searchArticles(query: string, options: {
    category?: string;
    difficulty?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<{ articles: ArticleMetadata[]; total: number }> {
    const index = await this.getArticleIndex();
    const searchTerm = query.toLowerCase();

    let matchedArticles = index.articles.filter(article => {
      const titleMatch = article.title.toLowerCase().includes(searchTerm);
      const descriptionMatch = article.description.toLowerCase().includes(searchTerm);
      const tagMatch = article.tags.some(tag => 
        tag.toLowerCase().includes(searchTerm)
      );
      
      return titleMatch || descriptionMatch || tagMatch;
    });

    // 应用额外筛选条件
    if (options.category) {
      matchedArticles = matchedArticles.filter(
        article => article.category === options.category
      );
    }

    if (options.difficulty) {
      matchedArticles = matchedArticles.filter(
        article => article.difficulty === options.difficulty
      );
    }

    const total = matchedArticles.length;

    // 应用分页
    const offset = options.offset || 0;
    const limit = options.limit || 20;
    const paginatedArticles = matchedArticles.slice(offset, offset + limit);

    return {
      articles: paginatedArticles,
      total,
    };
  }

  // 获取所有分类
  async getCategories(): Promise<string[]> {
    const index = await this.getArticleIndex();
    const categories = [...new Set(index.articles.map(article => article.category))];
    return categories.sort();
  }

  // 获取所有标签
  async getTags(): Promise<string[]> {
    const index = await this.getArticleIndex();
    const tagSet = new Set<string>();
    
    index.articles.forEach(article => {
      article.tags.forEach(tag => tagSet.add(tag));
    });
    
    return Array.from(tagSet).sort();
  }

  // 清理缓存
  clearCache(): void {
    this.indexCache = null;
    this.articleCache.clear();
    console.log('Data cache cleared');
  }

  // 获取缓存统计信息
  getCacheStats(): { indexCached: boolean; articlesCached: number; cacheSize: string } {
    const cacheSize = JSON.stringify({
      index: this.indexCache,
      articles: Array.from(this.articleCache.values()),
    }).length;

    return {
      indexCached: this.indexCache !== null,
      articlesCached: this.articleCache.size,
      cacheSize: `${Math.round(cacheSize / 1024)}KB`,
    };
  }
}

// 单例实例
export const dataService = new DataService();