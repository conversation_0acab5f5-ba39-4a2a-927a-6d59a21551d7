import { Router } from 'express';
import { dataService } from '../services/dataService';
import { cacheService } from '../services/cacheService';
import { createSuccessResponse } from '../utils/response';
import { ApiError } from '../utils/errors';
import { validatePagination, sanitizeSearchQuery } from '../utils/validation';

const router = Router();

// GET /api/search - 搜索文章
router.get('/', async (req, res, next) => {
  try {
    const {
      q,
      category,
      difficulty,
      limit,
      offset
    } = req.query;

    if (!q || typeof q !== 'string' || q.trim().length === 0) {
      throw new ApiError('Search query is required', 400, 'INVALID_PARAMS');
    }

    // 验证分页参数（搜索限制更严格）
    const pagination = validatePagination(limit as string, offset as string);
    if (pagination.limit > 50) {
      throw new ApiError('Search limit cannot exceed 50', 400, 'INVALID_PARAMS');
    }

    const query = sanitizeSearchQuery(q);

    // 生成缓存键
    const cacheKey = `search:${JSON.stringify(req.query)}`;
    
    // 尝试从缓存获取
    const cached = cacheService.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for search: ${cacheKey}`);
      return res.json(cached);
    }

    // 执行搜索
    const { articles, total } = await dataService.searchArticles(query, {
      category: category as string,
      difficulty: difficulty as string,
      limit: pagination.limit,
      offset: pagination.offset,
    });

    const response = createSuccessResponse({
      articles,
      total,
      query: q, // 返回原始查询字符串
    });

    // 缓存结果
    cacheService.set(cacheKey, response);
    console.log(`Cached search results: ${cacheKey}`);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

export { router as searchRouter };