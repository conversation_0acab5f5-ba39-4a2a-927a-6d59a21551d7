import { Router } from 'express';
import { dataService } from '../services/dataService';
import { cacheService } from '../services/cacheService';
import { createSuccessResponse, createErrorResponse } from '../utils/response';
import { ApiError } from '../utils/errors';
import { validatePagination, isValidJLPTLevel } from '../utils/validation';

const router = Router();

// GET /api/articles - 获取文章列表
router.get('/', async (req, res, next) => {
  try {
    const {
      category,
      difficulty,
      tags,
      limit,
      offset
    } = req.query;

    // 验证分页参数
    const pagination = validatePagination(limit as string, offset as string);
    if (!pagination.isValid) {
      throw new ApiError('Invalid pagination parameters', 400, 'INVALID_PARAMS');
    }

    // 生成缓存键
    const cacheKey = `articles:list:${JSON.stringify(req.query)}`;
    
    // 尝试从缓存获取
    const cached = cacheService.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for articles list: ${cacheKey}`);
      return res.json(cached);
    }

    // 处理标签参数
    const tagsArray = tags ? (tags as string).split(',').map(t => t.trim()) : undefined;

    // 从数据服务获取数据
    const { articles, total } = await dataService.getArticles({
      category: category as string,
      difficulty: difficulty as string,
      tags: tagsArray,
      limit: pagination.limit,
      offset: pagination.offset,
    });

    const index = await dataService.getArticleIndex();

    const response = createSuccessResponse({
      articles,
      total,
      version: index.version,
      lastUpdated: index.lastUpdated,
      currentArticle: index.currentArticle,
    });

    // 缓存结果
    cacheService.set(cacheKey, response);
    console.log(`Cached articles list: ${cacheKey}`);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

// GET /api/articles/:id - 获取文章详情
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!id) {
      throw new ApiError('Article ID is required', 400, 'INVALID_PARAMS');
    }

    // 生成缓存键
    const cacheKey = `articles:detail:${id}`;
    
    // 尝试从缓存获取
    const cached = cacheService.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for article detail: ${cacheKey}`);
      return res.json(cached);
    }

    // 检查文章是否存在
    const metadata = await dataService.getArticleMetadata(id);
    if (!metadata) {
      throw new ApiError('Article not found', 404, 'NOT_FOUND');
    }

    // 获取文章内容
    const article = await dataService.getArticleContent(id);
    if (!article) {
      throw new ApiError('Article content not found', 404, 'NOT_FOUND');
    }

    const response = createSuccessResponse(article);

    // 缓存结果
    cacheService.set(cacheKey, response);
    console.log(`Cached article detail: ${cacheKey}`);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

// GET /api/articles/:id/levels/:level - 获取特定级别数据
router.get('/:id/levels/:level', async (req, res, next) => {
  try {
    const { id, level } = req.params;

    if (!id || !level) {
      throw new ApiError('Article ID and level are required', 400, 'INVALID_PARAMS');
    }

    if (!isValidJLPTLevel(level)) {
      throw new ApiError('Invalid JLPT level', 400, 'INVALID_PARAMS');
    }

    // 生成缓存键
    const cacheKey = `articles:level:${id}:${level}`;
    
    // 尝试从缓存获取
    const cached = cacheService.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for level data: ${cacheKey}`);
      return res.json(cached);
    }

    // 获取文章内容
    const article = await dataService.getArticleContent(id);
    if (!article) {
      throw new ApiError('Article not found', 404, 'NOT_FOUND');
    }

    const levelData = article.jlptData[level];
    if (!levelData) {
      throw new ApiError('Level data not found', 404, 'NOT_FOUND');
    }

    const response = createSuccessResponse({
      articleId: id,
      level,
      data: levelData,
    });

    // 缓存结果
    cacheService.set(cacheKey, response);
    console.log(`Cached level data: ${cacheKey}`);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

export { router as articlesRouter };