export function validateQuery(query: any, validationFn?: (q: any) => boolean): { success: boolean; data?: any; error?: string } {
  try {
    if (validationFn && !validationFn(query)) {
      return { success: false, error: 'Invalid query parameters' };
    }
    return { success: true, data: query };
  } catch (error) {
    return { success: false, error: 'Validation failed' };
  }
}

export function validateParams(params: any): boolean {
  return params && typeof params === 'object';
}

export function isValidJLPTLevel(level: string): boolean {
  return ['N5', 'N4', 'N3', 'N2', 'N1'].includes(level);
}

export function isValidDifficulty(difficulty: string): boolean {
  return ['beginner', 'intermediate', 'advanced'].includes(difficulty);
}

export function sanitizeSearchQuery(query: string): string {
  return query.trim().toLowerCase();
}

export function validatePagination(limit?: string, offset?: string): { limit: number; offset: number; isValid: boolean } {
  const parsedLimit = parseInt(limit || '50') || 50;
  const parsedOffset = parseInt(offset || '0') || 0;
  
  const isValid = parsedLimit > 0 && parsedLimit <= 100 && parsedOffset >= 0;
  
  return {
    limit: Math.min(parsedLimit, 100),
    offset: Math.max(parsedOffset, 0),
    isValid,
  };
}