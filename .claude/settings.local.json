{"permissions": {"allow": ["Bash(find:*)", "Bash(npm run lint)", "Bash(grep:*)", "Ba<PERSON>(expo start:*)", "Bash(npm install:*)", "Bash(npm uninstall:*)", "Bash(expo lint:*)", "Bash(npm run typecheck:*)", "Bash(npx tsc:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(node:*)", "<PERSON><PERSON>(mkdir:*)", "WebFetch(domain:appstoreconnect.apple.com)", "WebFetch(domain:docs.expo.dev)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/migrate-to-monorepo.sh:*)", "Bash(npm run setup:*)", "Bash(for file in src/utils/dataUtils.ts src/components/FullTextModal.tsx src/components/LevelCard.tsx src/components/ArticleSelector.tsx src/components/JLPTLearning.tsx src/components/NavigationControls.tsx src/components/ProgressBar.tsx)", "Bash(do echo \"=== $file ===\")", "Bash(done)", "Bash(npm run type-check:*)", "Bash(mv data apps/mobile/)"], "deny": []}}