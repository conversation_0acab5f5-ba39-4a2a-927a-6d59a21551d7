# iOS App Store 上线全流程指南

本文档详细介绍如何将 Dokkai 日本语学习应用发布到 iOS App Store，包括从开发者账号申请到生产上线的完整流程。

## 目录
1. [Apple Developer 账号申请](#apple-developer-账号申请)
2. [Xcode 和开发环境设置](#xcode-和开发环境设置)
3. [App Store Connect 配置](#app-store-connect-配置)
4. [本地开发和测试](#本地开发和测试)
5. [证书和描述文件配置](#证书和描述文件配置)
6. [EAS Build 配置](#eas-build-配置)
7. [TestFlight 测试](#testflight-测试)
8. [App Store 审核和发布](#app-store-审核和发布)
9. [版本更新流程](#版本更新流程)
10. [常见问题和解决方案](#常见问题和解决方案)

## Apple Developer 账号申请

### 1. 准备材料
- Apple ID（个人或企业邮箱）
- 信用卡（用于支付年费 $99 USD）
- 企业账号还需要：
  - 企业营业执照
  - DUNS 编号
  - 法定代表人身份证明

### 2. 申请流程
1. 访问 [Apple Developer Program](https://developer.apple.com/programs/)
2. 点击 "Enroll" 开始申请
3. 使用 Apple ID 登录
4. 选择账号类型（个人/企业）
5. 填写开发者信息
6. 支付年费 $99 USD
7. 等待 Apple 审核（通常 24-48 小时）

### 3. 账号激活
- 收到 Apple 确认邮件后，账号即可使用
- 登录 [Apple Developer Portal](https://developer.apple.com/account/)
- 访问 [App Store Connect](https://appstoreconnect.apple.com/)

## Xcode 和开发环境设置

### 1. 安装 Xcode
```bash
# 从 App Store 安装 Xcode（推荐）
# 或使用命令行工具
xcode-select --install
```

### 2. 配置开发团队
1. 打开 Xcode
2. Preferences → Accounts
3. 添加 Apple ID
4. 选择开发团队

### 3. Expo CLI 设置
```bash
# 确保已安装最新版本
npm install -g @expo/cli

# 登录 Expo 账号
expo login

# 验证 EAS CLI
npm install -g eas-cli
eas login
```

## App Store Connect 配置

### 1. 创建应用
1. 登录 [App Store Connect](https://appstoreconnect.apple.com/)
2. 点击 "My Apps" → "+" → "New App"
3. 填写应用信息：
   - **Name**: 読解 - 日本語学習アプリ
   - **Bundle ID**: com.interjc.dokkai（需要在 Developer Portal 先注册）
   - **Language**: Japanese
   - **Platform**: iOS

### 2. Bundle Identifier 注册
1. 访问 [Developer Portal](https://developer.apple.com/account/)
2. Certificates, Identifiers & Profiles → Identifiers
3. 点击 "+" 创建新的 App ID
4. 选择 "App IDs" → "App"
5. 输入：
   - **Description**: Dokkai Japanese Learning App
   - **Bundle ID**: com.interjc.dokkai
6. 选择所需的 App Services（推荐启用 Push Notifications）

### 3. 应用信息配置
在 App Store Connect 中配置：

#### App Information
- **Name**: 読解 - 日本語学習アプリ
- **Bundle ID**: com.interjc.dokkai
- **Primary Language**: Japanese
- **SKU**: dokkai-ios-v1
- **Category**: Education
- **Secondary Category**: Reference

#### Pricing and Availability
- **Price**: Free
- **Availability**: All regions
- **Schedule**: Make available immediately

## 本地开发和测试

### 1. iOS 模拟器测试
```bash
# 启动开发服务器
expo start

# 在 iOS 模拟器中运行
expo start --ios

# 或使用特定模拟器
expo start --ios --simulator="iPhone 15 Pro"
```

### 2. 物理设备测试
1. 在 iPhone/iPad 上安装 Expo Go 应用
2. 使用相同 WiFi 网络
3. 扫描 QR 码或输入链接

### 3. 本地构建测试
```bash
# 本地 iOS 构建（需要 macOS）
expo run:ios

# 指定设备
expo run:ios --device "Your iPhone Name"
```

### 4. 预发布检查清单
- [ ] 应用在不同屏幕尺寸上正常显示
- [ ] 所有功能正常工作
- [ ] 日语文本正确显示
- [ ] 无崩溃和性能问题
- [ ] 网络请求正常
- [ ] 数据加载和缓存机制工作正常

## 证书和描述文件配置

### 1. 开发证书
```bash
# EAS 会自动处理证书，但也可手动配置
eas credentials

# 查看当前凭据
eas credentials --platform=ios
```

### 2. 手动证书配置（可选）
如需手动配置：

1. **Development Certificate**
   - 在 Developer Portal 创建开发证书
   - 下载并安装到 Keychain

2. **Distribution Certificate**
   - 创建生产发布证书
   - 用于 App Store 发布

3. **Provisioning Profiles**
   - Development Profile（开发测试）
   - Ad Hoc Profile（设备测试）
   - App Store Profile（商店发布）

## EAS Build 配置

### 1. 初始化 EAS
```bash
# 在项目根目录执行
eas build:configure

# 这会创建 eas.json 配置文件
```

### 2. 更新 eas.json 配置
```json
{
  "cli": {
    "version": ">= 13.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "ios": {
        "resourceClass": "m-medium"
      }
    },
    "preview": {
      "distribution": "internal",
      "ios": {
        "resourceClass": "m-medium",
        "simulator": true
      }
    },
    "production": {
      "ios": {
        "resourceClass": "m-medium"
      }
    }
  },
  "submit": {
    "production": {}
  }
}
```

### 3. 更新 app.config.js
确保包含必要的 iOS 配置：
```javascript
export default {
  expo: {
    name: "読解 - 日本語学習アプリ",
    slug: "dokkai",
    owner: "interjc",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "dokkai",
    userInterfaceStyle: "automatic",
    newArchEnabled: true,
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.interjc.dokkai",
      buildNumber: "1"
    },
    // ... 其他配置
    extra: {
      eas: {
        projectId: "1c8ce928-31e7-40e9-b2d3-273e867521fb"
      }
    }
  }
};
```

### 4. 构建应用
```bash
# 开发构建
eas build --platform ios --profile development

# 预览构建（用于内部测试）
eas build --platform ios --profile preview

# 生产构建（用于 App Store）
eas build --platform ios --profile production
```

## TestFlight 测试

### 1. 构建和上传
```bash
# 构建生产版本
eas build --platform ios --profile production

# 或直接提交到 App Store Connect
eas submit --platform ios
```

### 2. TestFlight 配置
在 App Store Connect 中：

1. 进入应用页面
2. 选择 "TestFlight" 标签
3. 等待构建版本出现并完成处理
4. 配置测试信息：

#### Test Information
- **What to Test**: 
  ```
  感谢测试 Dokkai 日本语学习应用！
  
  主要测试内容：
  1. 文章选择和加载功能
  2. JLPT 等级切换（N5-N1）
  3. 句子导航和进度保存
  4. 全文查看模式
  5. 应用性能和稳定性
  
  如有问题请通过 TestFlight 反馈。
  ```

- **App Privacy**: 选择隐私实践（根据实际数据收集情况）

### 3. 添加测试员
1. **内部测试组** (Internal Testing)
   - 自动包含开发团队成员
   - 最多 100 名内部测试员

2. **外部测试组** (External Testing)
   - 点击 "External Testing" → "+"
   - 创建测试组：
     - **Group Name**: Dokkai Beta Testers
     - **Public Link**: 启用（可选）
   - 添加测试员邮箱
   - 提交 Beta 审核（通常 24-48 小时）

### 4. 测试反馈收集
- 监控崩溃报告
- 收集用户反馈
- 分析性能数据
- 修复发现的问题

## App Store 审核和发布

### 1. 应用元数据配置

#### App Store 页面信息
在 App Store Connect 中完善：

**App Store 标题页**
- **App Name**: 読解 - 日本語学習アプリ
- **Subtitle**: JLPT N5-N1 段階学習
- **Description**:
  ```
  JLPTの段階的学習を実現する革新的な日本語学習アプリです。

  【主な機能】
  ✅ 同じ内容をN5からN1まで段階的に比較学習
  ✅ 豊富な日常生活シーンの記事
  ✅ 文法レベルの可視化
  ✅ 学習進度の自動保存
  ✅ オフライン学習対応

  【学習効果】
  • 段階的な難易度上昇で自然な日本語習得
  • 同じトピックでレベル間の表現差を理解
  • 実用的な日常会話スキルの向上

  【対象】
  • JLPT受験準備者
  • 日本語学習初心者から上級者
  • 日本での生活準備者

  効率的な日本語学習を今すぐ始めましょう！
  ```

- **Keywords**: JLPT,日本語学習,N5,N4,N3,N2,N1,日本語能力試験,学習アプリ,Japanese
- **Support URL**: https://yourdomain.com/support（需要创建）
- **Marketing URL**: https://yourdomain.com（可选）

#### 应用截图
准备不同尺寸的截图：

**6.7" Display (iPhone 15 Pro Max)**
- 2778 x 1284 像素
- 至少 3 张，最多 10 张

**6.1" Display (iPhone 15 Pro)**
- 2556 x 1179 像素

**5.5" Display (iPhone 8 Plus)**
- 2208 x 1242 像素

**截图建议内容：**
1. 主界面展示文章列表
2. JLPT 等级选择界面
3. 学习界面展示
4. 全文查看模式
5. 应用设置页面

#### 应用图标和元数据
- **App Icon**: 1024 x 1024 像素（已有：assets/images/icon.png）
- **App Preview**: 可选的应用视频预览
- **Category**: Education（主分类）
- **Secondary Category**: Reference
- **Content Rating**: 4+

### 2. 版本发布配置

#### Version Information
- **Version**: 1.0.0
- **Build**: 1
- **Copyright**: 2025 DOKKAI
- **What's New in This Version**:
  ```
  🎉 Dokkai 日本語学習アプリの初回リリース！

  ✨ 新機能
  • N5からN1まで段階的比較学習
  • 30以上の日常生活記事
  • 自動進度保存
  • オフライン学習対応

  📚 学習コンテンツ
  • 学生生活、仕事、旅行などの実用的なトピック
  • 各レベルでの文法と語彙の可視化
  • 文章とセンテンスレベルでの詳細学習

  皆様の日本語学習をサポートします！
  ```

#### App Review Information
- **Contact Information**:
  - First Name: [你的姓名]
  - Last Name: [你的姓名]
  - Phone: +81-XXX-XXXX-XXXX
  - Email: <EMAIL>

- **Demo Account**: 不需要（应用无需登录）
- **Notes**: 
  ```
  这是一个日本语学习应用，专注于JLPT考试准备。应用使用本地数据，
  无需网络连接即可正常使用所有功能。
  ```

### 3. 提交审核
1. 确保所有必填项已完成
2. 点击 "Submit for Review"
3. 等待审核结果（通常 24-48 小时）

### 4. 审核状态追踪
在 App Store Connect 监控状态：
- **Waiting for Review**: 等待审核
- **In Review**: 审核中
- **Metadata Rejected**: 元数据被拒
- **Binary Rejected**: 二进制文件被拒
- **Ready for Sale**: 审核通过，可发布

### 5. 发布上线
审核通过后：
1. 选择发布方式：
   - **Automatically release**: 自动发布
   - **Manually release**: 手动发布
2. 应用正式上线 App Store

## 版本更新流程

### 1. 版本号规划
使用语义化版本号：
- **主版本号**：重大功能变更
- **次版本号**：新功能添加
- **修订版本号**：bug 修复

示例：1.0.0 → 1.1.0 → 1.1.1

### 2. 更新 app.config.js
```javascript
export default {
  expo: {
    name: "読解 - 日本語学習アプリ",
    version: "1.1.0", // 更新版本号
    ios: {
      buildNumber: "2" // 递增构建号
    }
    // ... 其他配置
  }
};
```

### 3. 构建和提交
```bash
# 构建新版本
eas build --platform ios --profile production

# 提交到 App Store
eas submit --platform ios
```

### 4. 版本更新说明
在 App Store Connect 中更新 "What's New":
```
📈 バージョン 1.1.0 の新機能

✨ 新機能
• 新しい記事を10本追加
• 学習統計機能
• ダークモード対応

🐛 修正
• パフォーマンスの向上
• 軽微なバグの修正

ご利用いただきありがとうございます！
```

## 常见问题和解决方案

### 1. 构建问题

**问题：构建失败**
```bash
# 查看详细错误日志
eas build --platform ios --profile production --clear-cache

# 清理本地缓存
expo doctor
npm cache clean --force
```

**问题：证书错误**
```bash
# 重新配置证书
eas credentials --platform ios

# 删除并重新创建证书
eas credentials --platform ios --clear-provisioning-profile
```

### 2. 审核被拒

**常见拒绝原因：**
1. **元数据问题**：描述不准确或包含违禁词汇
2. **功能问题**：应用崩溃或功能不完整
3. **隐私问题**：缺少隐私政策或数据使用说明
4. **设计问题**：界面不符合 iOS 设计规范

**解决方案：**
1. 仔细阅读拒绝理由
2. 修复相关问题
3. 重新提交审核

### 3. 性能优化

**内存优化**
```javascript
// 在组件中优化数据加载
const [articles, setArticles] = useState([]);

useEffect(() => {
  // 分页加载数据
  const loadArticles = async () => {
    const data = await articleService.loadArticles({
      limit: 10,
      offset: 0
    });
    setArticles(data);
  };
  
  loadArticles();
}, []);
```

**图片优化**
```javascript
// 使用适当的图片尺寸
<Image
  source={require('./assets/icon.png')}
  style={{ width: 100, height: 100 }}
  resizeMode="contain"
/>
```

### 4. 国际化支持

**添加多语言支持**
```javascript
// app.config.js
export default {
  expo: {
    locales: {
      ja: "Japanese",
      en: "English"
    }
  }
};
```

### 5. 调试工具

**使用 Flipper 调试**
```bash
# 安装 Flipper
npx react-native init --template react-native-template-typescript

# 启用调试
expo start --dev-client
```

**性能监控**
```javascript
// 添加性能监控
import { Performance } from '@react-native-async-storage/async-storage';

const startTime = Performance.now();
// 执行操作
const endTime = Performance.now();
console.log(`操作耗时: ${endTime - startTime}ms`);
```

## 总结

iOS App Store 上线是一个复杂但有序的过程。关键点包括：

1. **前期准备充分**：开发者账号、证书配置、应用信息
2. **测试彻底**：本地测试、TestFlight 测试、用户反馈
3. **元数据完善**：描述、截图、关键词优化
4. **审核配合**：及时响应审核意见，快速修复问题
5. **持续迭代**：定期更新功能，优化用户体验

遵循本指南，Dokkai 应用将能够顺利发布到 App Store，为日本语学习者提供优质的学习工具。

---

**重要提醒**：
- 保持开发者账号年费缴纳
- 定期检查证书过期时间
- 关注 Apple 政策更新
- 维护良好的用户评价和反馈