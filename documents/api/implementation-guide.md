# Dokkai Mock API 实现指南

## 概述

本文档详细描述了如何创建 Mock API 来模拟从 D1 和 R2 读取数据的行为。实际上 API 内部直接读取现有的 JSON 文件，以便快速开发和测试前端功能。

## 技术栈

- **Express.js**: API 服务器
- **TypeScript**: 开发语言
- **JSON 文件**: 数据存储（模拟 D1 + R2）
- **cors**: 跨域支持

## 项目结构

```
api-server/
├── src/
│   ├── index.ts                 # API 服务器入口文件
│   ├── routes/
│   │   ├── articles.ts          # 文章相关路由
│   │   ├── search.ts            # 搜索路由
│   │   └── metadata.ts          # 元数据路由
│   ├── services/
│   │   ├── dataService.ts       # 数据读取服务（读取JSON文件）
│   │   └── cacheService.ts      # 内存缓存服务
│   ├── types/
│   │   └── api.ts               # API 类型定义
│   └── utils/
│       ├── response.ts          # 响应工具函数
│       ├── validation.ts        # 数据验证
│       └── errors.ts            # 错误处理
├── package.json
├── tsconfig.json
└── README.md
```

## 环境配置

### package.json

```json
{
  "name": "dokkai-api-server",
  "version": "1.0.0",
  "scripts": {
    "dev": "ts-node src/index.ts",
    "build": "tsc",
    "start": "node dist/index.js"
  },
  "dependencies": {
    "express": "^4.18.0",
    "cors": "^2.8.5",
    "compression": "^1.7.4"
  },
  "devDependencies": {
    "@types/express": "^4.17.0",
    "@types/cors": "^2.8.0",
    "@types/compression": "^1.7.0",
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0",
    "ts-node": "^10.9.0"
  }
}
```

### tsconfig.json

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

## 数据库架构

### D1 数据库表结构

```sql
-- articles 表
CREATE TABLE articles (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  author TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT,
  tags TEXT NOT NULL,           -- JSON数组字符串
  category TEXT NOT NULL,
  difficulty TEXT NOT NULL,     -- 'beginner' | 'intermediate' | 'advanced'
  estimated_reading_time INTEGER NOT NULL,
  sentence_count INTEGER NOT NULL,
  r2_key TEXT NOT NULL         -- R2中对应文件的键名
);

-- 创建索引以优化查询性能
CREATE INDEX idx_articles_category ON articles(category);
CREATE INDEX idx_articles_difficulty ON articles(difficulty);
CREATE INDEX idx_articles_created_at ON articles(created_at);

-- app_settings 表
CREATE TABLE app_settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  updated_at TEXT NOT NULL
);

-- 插入应用设置初始数据
INSERT INTO app_settings (key, value, updated_at) VALUES
('version', '1.0.0', datetime('now')),
('current_article', 'daily-life-student', datetime('now'));
```

## 核心实现

### 1. Worker 入口文件 (src/index.ts)

```typescript
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { cache } from 'hono/cache';
import { articlesRouter } from './routes/articles';
import { searchRouter } from './routes/search';
import { metadataRouter } from './routes/metadata';
import { handleError } from './utils/errors';

export interface Env {
  DB: D1Database;
  STORAGE: R2Bucket;
  CACHE: KVNamespace;
}

const app = new Hono<{ Bindings: Env }>();

// 中间件
app.use('*', cors({
  origin: ['https://dokkai.com', 'https://www.dokkai.com', 'http://localhost:3000'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

app.use('*', logger());

// 缓存中间件 - 5分钟缓存
app.use('/api/*', cache({
  cacheName: 'dokkai-api-cache',
  cacheControl: 'max-age=300',
}));

// 路由
app.route('/api/articles', articlesRouter);
app.route('/api/search', searchRouter);
app.route('/api', metadataRouter);

// 健康检查
app.get('/', (c) => {
  return c.json({
    success: true,
    message: 'Dokkai API is running',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
  });
});

// 全局错误处理
app.onError((err, c) => {
  return handleError(err, c);
});

// 404 处理
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'Endpoint not found',
    code: 'NOT_FOUND',
  }, 404);
});

export default app;
```

### 2. 数据库服务 (src/services/database.ts)

```typescript
import { ArticleMetadata } from '../types/api';

export class DatabaseService {
  constructor(private db: D1Database) {}

  async getArticles(options: {
    category?: string;
    difficulty?: string;
    tags?: string[];
    limit?: number;
    offset?: number;
  } = {}): Promise<{ articles: ArticleMetadata[]; total: number }> {
    const { category, difficulty, tags, limit = 50, offset = 0 } = options;

    let query = `
      SELECT * FROM articles
      WHERE 1=1
    `;
    const params: string[] = [];

    if (category) {
      query += ` AND category = ?`;
      params.push(category);
    }

    if (difficulty) {
      query += ` AND difficulty = ?`;
      params.push(difficulty);
    }

    if (tags && tags.length > 0) {
      const tagConditions = tags.map(() => `tags LIKE ?`).join(' OR ');
      query += ` AND (${tagConditions})`;
      tags.forEach(tag => params.push(`%"${tag}"%`));
    }

    // 计算总数
    const countQuery = query.replace('SELECT *', 'SELECT COUNT(*) as count');
    const countResult = await this.db.prepare(countQuery).bind(...params).first();
    const total = countResult?.count || 0;

    // 获取分页数据
    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit.toString(), offset.toString());

    const result = await this.db.prepare(query).bind(...params).all();
    
    const articles: ArticleMetadata[] = result.results.map((row: any) => ({
      id: row.id,
      title: row.title,
      description: row.description,
      author: row.author,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      tags: JSON.parse(row.tags),
      category: row.category,
      difficulty: row.difficulty,
      estimatedReadingTime: row.estimated_reading_time,
      sentenceCount: row.sentence_count,
    }));

    return { articles, total };
  }

  async getArticleById(id: string): Promise<ArticleMetadata | null> {
    const result = await this.db
      .prepare('SELECT * FROM articles WHERE id = ?')
      .bind(id)
      .first();

    if (!result) return null;

    return {
      id: result.id,
      title: result.title,
      description: result.description,
      author: result.author,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
      tags: JSON.parse(result.tags),
      category: result.category,
      difficulty: result.difficulty,
      estimatedReadingTime: result.estimated_reading_time,
      sentenceCount: result.sentence_count,
    };
  }

  async searchArticles(query: string, options: {
    category?: string;
    difficulty?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<{ articles: ArticleMetadata[]; total: number }> {
    const { category, difficulty, limit = 20, offset = 0 } = options;

    let sql = `
      SELECT * FROM articles
      WHERE (title LIKE ? OR description LIKE ? OR tags LIKE ?)
    `;
    const params = [`%${query}%`, `%${query}%`, `%${query}%`];

    if (category) {
      sql += ` AND category = ?`;
      params.push(category);
    }

    if (difficulty) {
      sql += ` AND difficulty = ?`;
      params.push(difficulty);
    }

    // 计算总数
    const countQuery = sql.replace('SELECT *', 'SELECT COUNT(*) as count');
    const countResult = await this.db.prepare(countQuery).bind(...params).first();
    const total = countResult?.count || 0;

    // 获取分页数据
    sql += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit.toString(), offset.toString());

    const result = await this.db.prepare(sql).bind(...params).all();
    
    const articles: ArticleMetadata[] = result.results.map((row: any) => ({
      id: row.id,
      title: row.title,
      description: row.description,
      author: row.author,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      tags: JSON.parse(row.tags),
      category: row.category,
      difficulty: row.difficulty,
      estimatedReadingTime: row.estimated_reading_time,
      sentenceCount: row.sentence_count,
    }));

    return { articles, total };
  }

  async getCategories(): Promise<string[]> {
    const result = await this.db
      .prepare('SELECT DISTINCT category FROM articles ORDER BY category')
      .all();
    
    return result.results.map((row: any) => row.category);
  }

  async getTags(): Promise<string[]> {
    const result = await this.db
      .prepare('SELECT tags FROM articles')
      .all();
    
    const tagSet = new Set<string>();
    result.results.forEach((row: any) => {
      const tags = JSON.parse(row.tags);
      tags.forEach((tag: string) => tagSet.add(tag));
    });
    
    return Array.from(tagSet).sort();
  }

  async getAppSettings(): Promise<{ version: string; currentArticle: string; lastUpdated: string }> {
    const settings = await this.db
      .prepare('SELECT key, value FROM app_settings WHERE key IN (?, ?)')
      .bind('version', 'current_article')
      .all();

    const settingsMap = new Map(
      settings.results.map((row: any) => [row.key, row.value])
    );

    return {
      version: settingsMap.get('version') || '1.0.0',
      currentArticle: settingsMap.get('current_article') || '',
      lastUpdated: new Date().toISOString(),
    };
  }
}
```

### 3. 存储服务 (src/services/storage.ts)

```typescript
import { Article } from '../types/api';

export class StorageService {
  constructor(private storage: R2Bucket) {}

  async getArticleContent(articleId: string): Promise<Article | null> {
    try {
      const key = `articles/${articleId}.json`;
      const object = await this.storage.get(key);
      
      if (!object) {
        return null;
      }

      const content = await object.text();
      const article = JSON.parse(content) as Article;
      
      return article;
    } catch (error) {
      console.error(`Error fetching article ${articleId}:`, error);
      return null;
    }
  }

  async saveArticleContent(articleId: string, article: Article): Promise<boolean> {
    try {
      const key = `articles/${articleId}.json`;
      const content = JSON.stringify(article, null, 2);
      
      await this.storage.put(key, content, {
        httpMetadata: {
          contentType: 'application/json',
        },
      });
      
      return true;
    } catch (error) {
      console.error(`Error saving article ${articleId}:`, error);
      return false;
    }
  }
}
```

### 4. 缓存服务 (src/services/cache.ts)

```typescript
export class CacheService {
  constructor(private kv: KVNamespace) {}

  private generateKey(prefix: string, params: Record<string, any>): string {
    const paramString = Object.keys(params)
      .sort()
      .map(key => `${key}:${params[key]}`)
      .join('|');
    return `${prefix}:${this.hash(paramString)}`;
  }

  private hash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.kv.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    try {
      await this.kv.put(key, JSON.stringify(value), {
        expirationTtl: ttl,
      });
    } catch (error) {
      console.error(`Cache set error for key ${key}:`, error);
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await this.kv.delete(key);
    } catch (error) {
      console.error(`Cache delete error for key ${key}:`, error);
    }
  }

  // 生成缓存键的辅助方法
  getArticleListKey(params: Record<string, any>): string {
    return this.generateKey('articles:list', params);
  }

  getArticleDetailKey(articleId: string): string {
    return `articles:detail:${articleId}`;
  }

  getLevelDataKey(articleId: string, level: string): string {
    return `articles:level:${articleId}:${level}`;
  }

  getCategoriesKey(): string {
    return 'categories:list';
  }

  getTagsKey(): string {
    return 'tags:list';
  }
}
```

### 5. 文章路由 (src/routes/articles.ts)

```typescript
import { Hono } from 'hono';
import { z } from 'zod';
import { DatabaseService } from '../services/database';
import { StorageService } from '../services/storage';
import { CacheService } from '../services/cache';
import { createSuccessResponse, createErrorResponse } from '../utils/response';
import { validateQuery } from '../utils/validation';
import { Env } from '../index';

const articlesRouter = new Hono<{ Bindings: Env }>();

// 查询参数验证模式
const articlesQuerySchema = z.object({
  category: z.string().optional(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  tags: z.string().optional(),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).optional(),
  offset: z.string().transform(Number).pipe(z.number().min(0)).optional(),
});

const levelParamSchema = z.object({
  id: z.string().min(1),
  level: z.enum(['N5', 'N4', 'N3', 'N2', 'N1']),
});

// GET /api/articles - 获取文章列表
articlesRouter.get('/', async (c) => {
  try {
    const queryResult = validateQuery(c.req.query(), articlesQuerySchema);
    if (!queryResult.success) {
      return createErrorResponse('Invalid query parameters', 'INVALID_PARAMS', 400);
    }

    const { category, difficulty, tags, limit, offset } = queryResult.data;
    const tagsArray = tags ? tags.split(',').map(t => t.trim()) : undefined;

    // 生成缓存键
    const cache = new CacheService(c.env.CACHE);
    const cacheKey = cache.getArticleListKey({ category, difficulty, tags, limit, offset });
    
    // 尝试从缓存获取
    const cached = await cache.get(cacheKey);
    if (cached) {
      return c.json(cached);
    }

    // 从数据库获取
    const db = new DatabaseService(c.env.DB);
    const { articles, total } = await db.getArticles({
      category,
      difficulty,
      tags: tagsArray,
      limit,
      offset,
    });

    const appSettings = await db.getAppSettings();

    const response = createSuccessResponse({
      articles,
      total,
      version: appSettings.version,
      lastUpdated: appSettings.lastUpdated,
      currentArticle: appSettings.currentArticle,
    });

    // 缓存结果（1小时）
    await cache.set(cacheKey, response, 3600);

    return c.json(response);
  } catch (error) {
    console.error('Articles list error:', error);
    return createErrorResponse('Internal server error', 'INTERNAL_ERROR', 500);
  }
});

// GET /api/articles/:id - 获取文章详情
articlesRouter.get('/:id', async (c) => {
  try {
    const articleId = c.req.param('id');
    
    if (!articleId) {
      return createErrorResponse('Article ID is required', 'INVALID_PARAMS', 400);
    }

    // 生成缓存键
    const cache = new CacheService(c.env.CACHE);
    const cacheKey = cache.getArticleDetailKey(articleId);
    
    // 尝试从缓存获取
    const cached = await cache.get(cacheKey);
    if (cached) {
      return c.json(cached);
    }

    // 从数据库检查文章是否存在
    const db = new DatabaseService(c.env.DB);
    const metadata = await db.getArticleById(articleId);
    
    if (!metadata) {
      return createErrorResponse('Article not found', 'NOT_FOUND', 404);
    }

    // 从 R2 获取完整内容
    const storage = new StorageService(c.env.STORAGE);
    const article = await storage.getArticleContent(articleId);
    
    if (!article) {
      return createErrorResponse('Article content not found', 'NOT_FOUND', 404);
    }

    const response = createSuccessResponse(article);

    // 缓存结果（1小时）
    await cache.set(cacheKey, response, 3600);

    return c.json(response);
  } catch (error) {
    console.error('Article detail error:', error);
    return createErrorResponse('Internal server error', 'INTERNAL_ERROR', 500);
  }
});

// GET /api/articles/:id/levels/:level - 获取特定级别数据
articlesRouter.get('/:id/levels/:level', async (c) => {
  try {
    const paramResult = levelParamSchema.safeParse({
      id: c.req.param('id'),
      level: c.req.param('level'),
    });

    if (!paramResult.success) {
      return createErrorResponse('Invalid parameters', 'INVALID_PARAMS', 400);
    }

    const { id: articleId, level } = paramResult.data;

    // 生成缓存键
    const cache = new CacheService(c.env.CACHE);
    const cacheKey = cache.getLevelDataKey(articleId, level);
    
    // 尝试从缓存获取
    const cached = await cache.get(cacheKey);
    if (cached) {
      return c.json(cached);
    }

    // 从 R2 获取文章内容
    const storage = new StorageService(c.env.STORAGE);
    const article = await storage.getArticleContent(articleId);
    
    if (!article) {
      return createErrorResponse('Article not found', 'NOT_FOUND', 404);
    }

    const levelData = article.jlptData[level];
    if (!levelData) {
      return createErrorResponse('Level data not found', 'NOT_FOUND', 404);
    }

    const response = createSuccessResponse({
      articleId,
      level,
      data: levelData,
    });

    // 缓存结果（1小时）
    await cache.set(cacheKey, response, 3600);

    return c.json(response);
  } catch (error) {
    console.error('Level data error:', error);
    return createErrorResponse('Internal server error', 'INTERNAL_ERROR', 500);
  }
});

export { articlesRouter };
```

## 数据迁移

### 迁移脚本

创建一个 Node.js 脚本来迁移现有数据：

```typescript
// scripts/migrate-data.ts
import fs from 'fs';
import path from 'path';

interface MigrationConfig {
  databaseId: string;
  bucketName: string;
  dataPath: string;
}

async function migrateData(config: MigrationConfig) {
  const indexPath = path.join(config.dataPath, 'index.json');
  const articlesPath = path.join(config.dataPath, 'articles');
  
  // 读取 index.json
  const indexData = JSON.parse(fs.readFileSync(indexPath, 'utf-8'));
  
  // 上传文章文件到 R2
  for (const article of indexData.articles) {
    const articlePath = path.join(articlesPath, `${article.id}.json`);
    if (fs.existsSync(articlePath)) {
      const articleContent = fs.readFileSync(articlePath, 'utf-8');
      // 使用 wrangler 或 AWS SDK 上传到 R2
      console.log(`Uploading ${article.id}.json to R2...`);
    }
  }
  
  // 插入数据到 D1
  for (const article of indexData.articles) {
    const sql = `
      INSERT INTO articles (
        id, title, description, author, created_at, updated_at,
        tags, category, difficulty, estimated_reading_time,
        sentence_count, r2_key
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    console.log(`Inserting article ${article.id} to D1...`);
    // 执行 SQL 插入
  }
}
```

## 部署步骤

1. **创建 Cloudflare 服务**:
   ```bash
   wrangler d1 create dokkai-db
   wrangler r2 bucket create dokkai-articles
   wrangler kv:namespace create "CACHE"
   ```

2. **更新 wrangler.toml** 配置文件

3. **运行数据库迁移**:
   ```bash
   wrangler d1 execute dokkai-db --file=./schema.sql
   ```

4. **迁移数据**:
   ```bash
   npm run migrate
   ```

5. **部署 Worker**:
   ```bash
   wrangler deploy
   ```

## 监控和日志

### 日志记录
Worker 自动记录请求日志，可通过以下命令查看：
```bash
wrangler tail
```

### 性能监控
- 使用 Cloudflare Analytics 监控请求量和延迟
- 设置 Alert 监控错误率和响应时间
- 定期检查 D1 和 R2 的使用量

### 错误追踪
- 实现结构化日志记录
- 设置错误告警机制
- 定期分析错误模式并优化

这个实现指南提供了完整的 API 后端架构，可以高效地处理文章数据的读取和缓存，确保良好的性能和用户体验。