# Dokkai API 设计文档

## 概述

本文档描述了 Dokkai 日语学习应用的 API 接口设计。数据存储架构如下：
- **D1 数据库**: 存储文章元数据索引 (原 index.json 内容)
- **R2 存储**: 存储完整的文章内容 JSON 文件
- **Worker**: 提供统一的 RESTful API 接口

## 数据架构

### D1 数据库表结构

#### articles 表
```sql
CREATE TABLE articles (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  author TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT,
  tags TEXT NOT NULL,           -- JSON数组字符串
  category TEXT NOT NULL,
  difficulty TEXT NOT NULL,     -- 'beginner' | 'intermediate' | 'advanced'
  estimated_reading_time INTEGER NOT NULL,
  sentence_count INTEGER NOT NULL,
  r2_key TEXT NOT NULL         -- R2中对应文件的键名
);
```

#### app_settings 表
```sql
CREATE TABLE app_settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  updated_at TEXT NOT NULL
);
```

### R2 存储结构
```
articles/
├── daily-life-student.json
├── school-activities.json
├── travel-adventure.json
└── ...
```

## API 接口设计

### 基础信息

- **Base URL**: `https://api.dokkai.example.com`
- **Content-Type**: `application/json`
- **认证**: 暂不需要（公开API）

---

## 1. 获取文章列表

### GET /api/articles

获取所有文章的元数据列表。

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|-----|------|------|------|
| category | string | 否 | 筛选分类 |
| difficulty | string | 否 | 筛选难度: beginner/intermediate/advanced |
| tags | string | 否 | 筛选标签，多个用逗号分隔 |
| limit | number | 否 | 限制返回数量，默认50 |
| offset | number | 否 | 偏移量，默认0 |

#### 响应格式

```typescript
interface ArticleListResponse {
  success: boolean;
  data: {
    articles: ArticleMetadata[];
    total: number;
    version: string;
    lastUpdated: string;
    currentArticle: string;
  };
  error?: string;
}

interface ArticleMetadata {
  id: string;
  title: string;
  description: string;
  author?: string;
  createdAt: string;
  updatedAt?: string;
  tags: string[];
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadingTime: number;
  sentenceCount: number;
}
```

#### 示例

**请求:**
```
GET /api/articles?category=daily-life&difficulty=beginner&limit=10
```

**响应:**
```json
{
  "success": true,
  "data": {
    "articles": [
      {
        "id": "daily-life-student",
        "title": "大学生の日常生活",
        "description": "大学生の朝から夜までの一日を描いたシンプルなストーリー。日常生活の基本を学びましょう。",
        "author": "Dokkai Team",
        "createdAt": "2025-01-22T00:00:00.000Z",
        "tags": ["日常生活", "大学生", "基础会話"],
        "category": "daily-life",
        "difficulty": "beginner",
        "estimatedReadingTime": 5,
        "sentenceCount": 10
      }
    ],
    "total": 45,
    "version": "1.0.0",
    "lastUpdated": "2025-07-22T00:00:00.000Z",
    "currentArticle": "daily-life-student"
  }
}
```

---

## 2. 获取文章详情

### GET /api/articles/:id

获取指定文章的完整内容，包括所有JLPT级别的数据。

#### 路径参数

| 参数 | 类型 | 必填 | 说明 |
|-----|------|------|------|
| id | string | 是 | 文章ID |

#### 响应格式

```typescript
interface ArticleDetailResponse {
  success: boolean;
  data: Article;
  error?: string;
}

interface Article {
  id: string;
  title: string;
  description: string;
  author?: string;
  createdAt: string;
  updatedAt?: string;
  tags: string[];
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadingTime: number;
  jlptData: JLPTData;
}

interface JLPTData {
  N5: LevelData;
  N4: LevelData;
  N3: LevelData;
  N2: LevelData;
  N1: LevelData;
}

interface LevelData {
  sentences: string[];
  grammar_features: string[];
  full_text: string;
  grammar_points: string[];
  word_count: number;
  color: string;
  bgColor: string;
  description: string;
}
```

#### 示例

**请求:**
```
GET /api/articles/daily-life-student
```

**响应:**
```json
{
  "success": true,
  "data": {
    "id": "daily-life-student",
    "title": "大学生の日常生活",
    "description": "描述一个大学生从早晨起床到晚上休息的典型一天...",
    "author": "Dokkai Team",
    "createdAt": "2025-01-22T00:00:00.000Z",
    "tags": ["日常生活", "大学生活", "基础会话"],
    "category": "daily-life",
    "difficulty": "beginner",
    "estimatedReadingTime": 5,
    "jlptData": {
      "N5": {
        "sentences": ["はじめまして。私の名前は田中です。", "..."],
        "grammar_features": ["基本自己介绍", "日常作息", "..."],
        "full_text": "はじめまして。私の名前は田中です。...",
        "grammar_points": ["です/である調", "時間表現", "..."],
        "word_count": 95,
        "color": "#22c55e",
        "bgColor": "#f0fdf4",
        "description": "入门级：简单句式，基础语法"
      },
      "N4": { /* ... */ },
      "N3": { /* ... */ },
      "N2": { /* ... */ },
      "N1": { /* ... */ }
    }
  }
}
```

---

## 3. 获取文章特定级别数据

### GET /api/articles/:id/levels/:level

获取指定文章特定JLPT级别的数据。

#### 路径参数

| 参数 | 类型 | 必填 | 说明 |
|-----|------|------|------|
| id | string | 是 | 文章ID |
| level | string | 是 | JLPT级别: N5/N4/N3/N2/N1 |

#### 响应格式

```typescript
interface LevelDataResponse {
  success: boolean;
  data: {
    articleId: string;
    level: Level;
    data: LevelData;
  };
  error?: string;
}
```

#### 示例

**请求:**
```
GET /api/articles/daily-life-student/levels/N5
```

**响应:**
```json
{
  "success": true,
  "data": {
    "articleId": "daily-life-student",
    "level": "N5",
    "data": {
      "sentences": ["はじめまして。私の名前は田中です。", "..."],
      "grammar_features": ["基本自己介绍", "日常作息", "..."],
      "full_text": "はじめまして。私の名前は田中です。...",
      "grammar_points": ["です/である調", "時間表現", "..."],
      "word_count": 95,
      "color": "#22c55e",
      "bgColor": "#f0fdf4",
      "description": "入门级：简单句式，基础语法"
    }
  }
}
```

---

## 4. 获取分类列表

### GET /api/categories

获取所有可用的文章分类。

#### 响应格式

```typescript
interface CategoriesResponse {
  success: boolean;
  data: {
    categories: string[];
  };
  error?: string;
}
```

#### 示例

**响应:**
```json
{
  "success": true,
  "data": {
    "categories": ["daily-life", "school-life", "travel", "work-life", "culture"]
  }
}
```

---

## 5. 获取标签列表

### GET /api/tags

获取所有可用的标签。

#### 响应格式

```typescript
interface TagsResponse {
  success: boolean;
  data: {
    tags: string[];
  };
  error?: string;
}
```

#### 示例

**响应:**
```json
{
  "success": true,
  "data": {
    "tags": ["日常生活", "大学生活", "基础会話", "旅行", "文化体验", "..."]
  }
}
```

---

## 6. 搜索文章

### GET /api/search

根据关键词搜索文章。

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|-----|------|------|------|
| q | string | 是 | 搜索关键词 |
| category | string | 否 | 筛选分类 |
| difficulty | string | 否 | 筛选难度 |
| limit | number | 否 | 限制返回数量，默认20 |
| offset | number | 否 | 偏移量，默认0 |

#### 响应格式

```typescript
interface SearchResponse {
  success: boolean;
  data: {
    articles: ArticleMetadata[];
    total: number;
    query: string;
  };
  error?: string;
}
```

#### 示例

**请求:**
```
GET /api/search?q=大学生&limit=5
```

**响应:**
```json
{
  "success": true,
  "data": {
    "articles": [
      {
        "id": "daily-life-student",
        "title": "大学生の日常生活",
        "description": "大学生の朝から夜までの一日を描いたシンプルなストーリー。",
        "category": "daily-life",
        "difficulty": "beginner",
        "estimatedReadingTime": 5,
        "sentenceCount": 10,
        "tags": ["日常生活", "大学生", "基础会話"],
        "createdAt": "2025-01-22T00:00:00.000Z",
        "author": "Dokkai Team"
      }
    ],
    "total": 1,
    "query": "大学生"
  }
}
```

---

## 错误处理

### 错误响应格式

所有API在出错时都会返回统一的错误格式：

```typescript
interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}
```

### 常见错误代码

| HTTP状态码 | 错误代码 | 说明 |
|------------|----------|------|
| 400 | INVALID_PARAMS | 请求参数无效 |
| 404 | NOT_FOUND | 资源不存在 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |
| 503 | SERVICE_UNAVAILABLE | 服务不可用 |

### 示例错误响应

```json
{
  "success": false,
  "error": "Article not found",
  "code": "NOT_FOUND"
}
```

---

## 缓存策略

### 缓存层级
1. **Edge Cache**: 5分钟缓存 API 响应
2. **KV Store**: 1小时缓存热点数据
3. **R2 Storage**: 持久化存储原始数据

### 缓存键命名规则
- 文章列表: `articles:list:{hash(params)}`
- 文章详情: `articles:detail:{articleId}`
- 级别数据: `articles:level:{articleId}:{level}`
- 分类列表: `categories:list`
- 标签列表: `tags:list`

---

## 性能考虑

### 响应时间目标
- 文章列表: < 200ms
- 文章详情: < 300ms  
- 搜索结果: < 500ms

### 数据传输优化
- 启用 gzip 压缩
- 合理使用 HTTP 缓存头
- 支持 ETag 验证
- 支持条件请求 (If-None-Match)

## 版本控制

API 版本通过 URL 路径管理：
- v1: `/api/v1/...`
- v2: `/api/v2/...`

当前版本为 v1，可通过省略版本号访问 (`/api/...` 等同于 `/api/v1/...`)。