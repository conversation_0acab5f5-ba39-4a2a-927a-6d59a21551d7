# Dokkai Mock API 实现指南

## 概述

本文档详细描述了如何创建 Mock API 来模拟从 D1 和 R2 读取数据的行为。实际上 API 内部直接读取现有的 JSON 文件，以便快速开发和测试前端功能。

## 技术栈

- **Express.js**: API 服务器
- **TypeScript**: 开发语言
- **JSON 文件**: 数据存储（模拟 D1 + R2）
- **cors**: 跨域支持

## 项目结构

```
api-server/
├── src/
│   ├── index.ts                 # API 服务器入口文件
│   ├── routes/
│   │   ├── articles.ts          # 文章相关路由
│   │   ├── search.ts            # 搜索路由
│   │   └── metadata.ts          # 元数据路由
│   ├── services/
│   │   ├── dataService.ts       # 数据读取服务（读取JSON文件）
│   │   └── cacheService.ts      # 内存缓存服务
│   ├── types/
│   │   └── api.ts               # API 类型定义
│   └── utils/
│       ├── response.ts          # 响应工具函数
│       ├── validation.ts        # 数据验证
│       └── errors.ts            # 错误处理
├── package.json
├── tsconfig.json
└── README.md
```

## 核心实现

### 1. API 服务器入口 (src/index.ts)

```typescript
import express from 'express';
import cors from 'cors';
import compression from 'compression';
import { articlesRouter } from './routes/articles';
import { searchRouter } from './routes/search';
import { metadataRouter } from './routes/metadata';
import { errorHandler } from './utils/errors';

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:19006', 'exp://localhost:19000'],
  credentials: true,
}));

app.use(compression());
app.use(express.json());

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// 路由
app.use('/api/articles', articlesRouter);
app.use('/api/search', searchRouter);
app.use('/api', metadataRouter);

// 健康检查
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Dokkai Mock API is running',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
  });
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
  });
});

// 全局错误处理
app.use(errorHandler);

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    code: 'NOT_FOUND',
  });
});

app.listen(PORT, () => {
  console.log(`Dokkai Mock API server is running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`API endpoint: http://localhost:${PORT}/api`);
});
```

### 2. 数据读取服务 (src/services/dataService.ts)

```typescript
import fs from 'fs';
import path from 'path';
import { ArticleMetadata, Article, ArticleIndex } from '../types/api';

export class DataService {
  private dataPath: string;
  private indexCache: ArticleIndex | null = null;
  private articleCache: Map<string, Article> = new Map();

  constructor() {
    // 假设 data 目录在项目根目录下
    this.dataPath = path.join(process.cwd(), '..', 'data');
  }

  // 获取文章索引（模拟 D1 数据库）
  async getArticleIndex(): Promise<ArticleIndex> {
    if (this.indexCache) {
      return this.indexCache;
    }

    try {
      const indexPath = path.join(this.dataPath, 'index.json');
      const indexData = JSON.parse(fs.readFileSync(indexPath, 'utf-8'));
      this.indexCache = indexData;
      return indexData;
    } catch (error) {
      console.error('Error reading index.json:', error);
      throw new Error('Failed to load article index');
    }
  }

  // 获取文章内容（模拟 R2 存储）
  async getArticleContent(articleId: string): Promise<Article | null> {
    // 检查缓存
    if (this.articleCache.has(articleId)) {
      return this.articleCache.get(articleId)!;
    }

    try {
      const articlePath = path.join(this.dataPath, 'articles', `${articleId}.json`);
      
      if (!fs.existsSync(articlePath)) {
        return null;
      }

      const articleData = JSON.parse(fs.readFileSync(articlePath, 'utf-8'));
      
      // 缓存文章内容
      this.articleCache.set(articleId, articleData);
      
      return articleData;
    } catch (error) {
      console.error(`Error reading article ${articleId}:`, error);
      return null;
    }
  }

  // 获取文章列表（支持筛选）
  async getArticles(options: {
    category?: string;
    difficulty?: string;
    tags?: string[];
    limit?: number;
    offset?: number;
  } = {}): Promise<{ articles: ArticleMetadata[]; total: number }> {
    const index = await this.getArticleIndex();
    let filteredArticles = [...index.articles];

    // 应用筛选条件
    if (options.category) {
      filteredArticles = filteredArticles.filter(
        article => article.category === options.category
      );
    }

    if (options.difficulty) {
      filteredArticles = filteredArticles.filter(
        article => article.difficulty === options.difficulty
      );
    }

    if (options.tags && options.tags.length > 0) {
      filteredArticles = filteredArticles.filter(article =>
        options.tags!.some(tag => article.tags.includes(tag))
      );
    }

    const total = filteredArticles.length;

    // 应用分页
    const offset = options.offset || 0;
    const limit = options.limit || 50;
    const paginatedArticles = filteredArticles.slice(offset, offset + limit);

    return {
      articles: paginatedArticles,
      total,
    };
  }

  // 获取单个文章元数据
  async getArticleMetadata(articleId: string): Promise<ArticleMetadata | null> {
    const index = await this.getArticleIndex();
    return index.articles.find(article => article.id === articleId) || null;
  }

  // 搜索文章
  async searchArticles(query: string, options: {
    category?: string;
    difficulty?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<{ articles: ArticleMetadata[]; total: number }> {
    const index = await this.getArticleIndex();
    const searchTerm = query.toLowerCase();

    let matchedArticles = index.articles.filter(article => {
      const titleMatch = article.title.toLowerCase().includes(searchTerm);
      const descriptionMatch = article.description.toLowerCase().includes(searchTerm);
      const tagMatch = article.tags.some(tag => 
        tag.toLowerCase().includes(searchTerm)
      );
      
      return titleMatch || descriptionMatch || tagMatch;
    });

    // 应用额外筛选条件
    if (options.category) {
      matchedArticles = matchedArticles.filter(
        article => article.category === options.category
      );
    }

    if (options.difficulty) {
      matchedArticles = matchedArticles.filter(
        article => article.difficulty === options.difficulty
      );
    }

    const total = matchedArticles.length;

    // 应用分页
    const offset = options.offset || 0;
    const limit = options.limit || 20;
    const paginatedArticles = matchedArticles.slice(offset, offset + limit);

    return {
      articles: paginatedArticles,
      total,
    };
  }

  // 获取所有分类
  async getCategories(): Promise<string[]> {
    const index = await this.getArticleIndex();
    const categories = [...new Set(index.articles.map(article => article.category))];
    return categories.sort();
  }

  // 获取所有标签
  async getTags(): Promise<string[]> {
    const index = await this.getArticleIndex();
    const tagSet = new Set<string>();
    
    index.articles.forEach(article => {
      article.tags.forEach(tag => tagSet.add(tag));
    });
    
    return Array.from(tagSet).sort();
  }

  // 清理缓存
  clearCache(): void {
    this.indexCache = null;
    this.articleCache.clear();
  }
}

// 单例实例
export const dataService = new DataService();
```

### 3. 内存缓存服务 (src/services/cacheService.ts)

```typescript
interface CacheItem {
  data: any;
  timestamp: number;
  ttl: number;
}

export class CacheService {
  private cache: Map<string, CacheItem> = new Map();
  private defaultTTL = 5 * 60 * 1000; // 5分钟

  set(key: string, data: any, ttl?: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // 清理过期缓存
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }

  // 获取缓存统计信息
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// 单例实例
export const cacheService = new CacheService();

// 定期清理过期缓存
setInterval(() => {
  cacheService.cleanup();
}, 60 * 1000); // 每分钟清理一次
```

### 4. 工具函数

#### 响应工具 (src/utils/response.ts)

```typescript
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  details?: any;
}

export function createSuccessResponse<T>(data: T): ApiResponse<T> {
  return {
    success: true,
    data,
  };
}

export function createErrorResponse(
  error: string,
  code?: string,
  details?: any
): ApiResponse {
  return {
    success: false,
    error,
    code,
    details,
  };
}
```

#### 错误处理 (src/utils/errors.ts)

```typescript
import { Request, Response, NextFunction } from 'express';
import { createErrorResponse } from './response';

export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  console.error('API Error:', error);

  if (error instanceof ApiError) {
    res.status(error.statusCode).json(
      createErrorResponse(error.message, error.code, error.details)
    );
  } else {
    res.status(500).json(
      createErrorResponse('Internal server error', 'INTERNAL_ERROR')
    );
  }
}
```

#### 数据验证 (src/utils/validation.ts)

```typescript
export function validateQuery(query: any, schema: any): { success: boolean; data?: any; error?: string } {
  try {
    const result = schema.safeParse(query);
    if (result.success) {
      return { success: true, data: result.data };
    } else {
      return { success: false, error: result.error.message };
    }
  } catch (error) {
    return { success: false, error: 'Validation failed' };
  }
}

export function validateParams(params: any): boolean {
  return params && typeof params === 'object';
}
```

### 5. 文章路由实现 (src/routes/articles.ts)

```typescript
import { Router } from 'express';
import { dataService } from '../services/dataService';
import { cacheService } from '../services/cacheService';
import { createSuccessResponse, createErrorResponse } from '../utils/response';
import { ApiError } from '../utils/errors';

const router = Router();

// GET /api/articles - 获取文章列表
router.get('/', async (req, res, next) => {
  try {
    const {
      category,
      difficulty,
      tags,
      limit = '50',
      offset = '0'
    } = req.query;

    // 验证参数
    const parsedLimit = parseInt(limit as string) || 50;
    const parsedOffset = parseInt(offset as string) || 0;

    if (parsedLimit > 100) {
      throw new ApiError('Limit cannot exceed 100', 400, 'INVALID_PARAMS');
    }

    // 生成缓存键
    const cacheKey = `articles:list:${JSON.stringify(req.query)}`;
    
    // 尝试从缓存获取
    const cached = cacheService.get(cacheKey);
    if (cached) {
      return res.json(cached);
    }

    // 处理标签参数
    const tagsArray = tags ? (tags as string).split(',').map(t => t.trim()) : undefined;

    // 从数据服务获取数据
    const { articles, total } = await dataService.getArticles({
      category: category as string,
      difficulty: difficulty as string,
      tags: tagsArray,
      limit: parsedLimit,
      offset: parsedOffset,
    });

    const index = await dataService.getArticleIndex();

    const response = createSuccessResponse({
      articles,
      total,
      version: index.version,
      lastUpdated: index.lastUpdated,
      currentArticle: index.currentArticle,
    });

    // 缓存结果
    cacheService.set(cacheKey, response);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

// GET /api/articles/:id - 获取文章详情
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!id) {
      throw new ApiError('Article ID is required', 400, 'INVALID_PARAMS');
    }

    // 生成缓存键
    const cacheKey = `articles:detail:${id}`;
    
    // 尝试从缓存获取
    const cached = cacheService.get(cacheKey);
    if (cached) {
      return res.json(cached);
    }

    // 检查文章是否存在
    const metadata = await dataService.getArticleMetadata(id);
    if (!metadata) {
      throw new ApiError('Article not found', 404, 'NOT_FOUND');
    }

    // 获取文章内容
    const article = await dataService.getArticleContent(id);
    if (!article) {
      throw new ApiError('Article content not found', 404, 'NOT_FOUND');
    }

    const response = createSuccessResponse(article);

    // 缓存结果
    cacheService.set(cacheKey, response);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

// GET /api/articles/:id/levels/:level - 获取特定级别数据
router.get('/:id/levels/:level', async (req, res, next) => {
  try {
    const { id, level } = req.params;

    if (!id || !level) {
      throw new ApiError('Article ID and level are required', 400, 'INVALID_PARAMS');
    }

    if (!['N5', 'N4', 'N3', 'N2', 'N1'].includes(level)) {
      throw new ApiError('Invalid JLPT level', 400, 'INVALID_PARAMS');
    }

    // 生成缓存键
    const cacheKey = `articles:level:${id}:${level}`;
    
    // 尝试从缓存获取
    const cached = cacheService.get(cacheKey);
    if (cached) {
      return res.json(cached);
    }

    // 获取文章内容
    const article = await dataService.getArticleContent(id);
    if (!article) {
      throw new ApiError('Article not found', 404, 'NOT_FOUND');
    }

    const levelData = article.jlptData[level];
    if (!levelData) {
      throw new ApiError('Level data not found', 404, 'NOT_FOUND');
    }

    const response = createSuccessResponse({
      articleId: id,
      level,
      data: levelData,
    });

    // 缓存结果
    cacheService.set(cacheKey, response);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

export { router as articlesRouter };
```

### 6. 搜索路由 (src/routes/search.ts)

```typescript
import { Router } from 'express';
import { dataService } from '../services/dataService';
import { cacheService } from '../services/cacheService';
import { createSuccessResponse } from '../utils/response';
import { ApiError } from '../utils/errors';

const router = Router();

// GET /api/search - 搜索文章
router.get('/', async (req, res, next) => {
  try {
    const {
      q,
      category,
      difficulty,
      limit = '20',
      offset = '0'
    } = req.query;

    if (!q || typeof q !== 'string' || q.trim().length === 0) {
      throw new ApiError('Search query is required', 400, 'INVALID_PARAMS');
    }

    const query = q.trim();
    const parsedLimit = parseInt(limit as string) || 20;
    const parsedOffset = parseInt(offset as string) || 0;

    if (parsedLimit > 50) {
      throw new ApiError('Search limit cannot exceed 50', 400, 'INVALID_PARAMS');
    }

    // 生成缓存键
    const cacheKey = `search:${JSON.stringify(req.query)}`;
    
    // 尝试从缓存获取
    const cached = cacheService.get(cacheKey);
    if (cached) {
      return res.json(cached);
    }

    // 执行搜索
    const { articles, total } = await dataService.searchArticles(query, {
      category: category as string,
      difficulty: difficulty as string,
      limit: parsedLimit,
      offset: parsedOffset,
    });

    const response = createSuccessResponse({
      articles,
      total,
      query,
    });

    // 缓存结果
    cacheService.set(cacheKey, response);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

export { router as searchRouter };
```

### 7. 元数据路由 (src/routes/metadata.ts)

```typescript
import { Router } from 'express';
import { dataService } from '../services/dataService';
import { cacheService } from '../services/cacheService';
import { createSuccessResponse } from '../utils/response';

const router = Router();

// GET /api/categories - 获取分类列表
router.get('/categories', async (req, res, next) => {
  try {
    // 尝试从缓存获取
    const cacheKey = 'categories:list';
    const cached = cacheService.get(cacheKey);
    if (cached) {
      return res.json(cached);
    }

    const categories = await dataService.getCategories();
    const response = createSuccessResponse({ categories });

    // 缓存结果（较长时间，因为分类变化不频繁）
    cacheService.set(cacheKey, response, 30 * 60 * 1000); // 30分钟

    res.json(response);
  } catch (error) {
    next(error);
  }
});

// GET /api/tags - 获取标签列表
router.get('/tags', async (req, res, next) => {
  try {
    // 尝试从缓存获取
    const cacheKey = 'tags:list';
    const cached = cacheService.get(cacheKey);
    if (cached) {
      return res.json(cached);
    }

    const tags = await dataService.getTags();
    const response = createSuccessResponse({ tags });

    // 缓存结果（较长时间，因为标签变化不频繁）
    cacheService.set(cacheKey, response, 30 * 60 * 1000); // 30分钟

    res.json(response);
  } catch (error) {
    next(error);
  }
});

// GET /api/stats - 获取统计信息
router.get('/stats', async (req, res, next) => {
  try {
    const cacheKey = 'stats:summary';
    const cached = cacheService.get(cacheKey);
    if (cached) {
      return res.json(cached);
    }

    const index = await dataService.getArticleIndex();
    const categories = await dataService.getCategories();
    const tags = await dataService.getTags();

    // 统计各难度级别的文章数量
    const difficultyStats = index.articles.reduce((acc, article) => {
      acc[article.difficulty] = (acc[article.difficulty] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // 统计各分类的文章数量
    const categoryStats = index.articles.reduce((acc, article) => {
      acc[article.category] = (acc[article.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const response = createSuccessResponse({
      totalArticles: index.articles.length,
      totalCategories: categories.length,
      totalTags: tags.length,
      difficultyDistribution: difficultyStats,
      categoryDistribution: categoryStats,
      version: index.version,
      lastUpdated: index.lastUpdated,
    });

    // 缓存结果
    cacheService.set(cacheKey, response, 10 * 60 * 1000); // 10分钟

    res.json(response);
  } catch (error) {
    next(error);
  }
});

export { router as metadataRouter };
```

## 使用方法

### 1. 设置项目

```bash
# 在项目根目录下创建 api-server 目录
mkdir api-server
cd api-server

# 初始化项目
npm init -y

# 安装依赖
npm install express cors compression
npm install -D @types/express @types/cors @types/compression @types/node typescript ts-node

# 复制上面的代码到对应文件
```

### 2. 启动服务器

```bash
# 开发模式
npm run dev

# 构建并启动
npm run build
npm start
```

### 3. 测试接口

```bash
# 获取文章列表
curl http://localhost:3001/api/articles

# 获取特定文章
curl http://localhost:3001/api/articles/daily-life-student

# 搜索文章
curl "http://localhost:3001/api/search?q=大学生"

# 获取分类
curl http://localhost:3001/api/categories
```

## 集成到现有项目

如果你想将这个 Mock API 集成到现有的 Dokkai 项目中：

1. **在项目根目录创建 `api-server` 目录**
2. **修改 package.json 添加脚本**:
   ```json
   {
     "scripts": {
       "api:dev": "cd api-server && npm run dev",
       "api:build": "cd api-server && npm run build"
     }
   }
   ```
3. **更新前端代码使用新的 API 端点**

这个 Mock API 完全模拟了 Cloudflare 架构的行为，但使用本地 JSON 文件作为数据源，便于快速开发和测试。