# React Native 客户端缓存策略

## 概述

本文档描述了 Dokkai 日语学习应用在 React Native 客户端的本地缓存策略，旨在最大化用户体验，减少网络依赖，支持离线使用。

## 缓存目标

- 减少 API 请求次数
- 提供近即时的内容访问
- 支持离线学习
- 智能预加载常用内容
- 管理存储空间使用

## 缓存架构

### 分层缓存系统

```
L1: 内存缓存 (Memory Cache)
├── 当前会话数据
├── 最近访问的文章
└── 用户状态信息

L2: AsyncStorage (持久化小数据)
├── 用户配置
├── 学习进度
└── 缓存元数据

L3: SQLite (持久化结构化数据)
├── 文章元数据
├── 完整文章内容
└── 音频元数据

L4: FileSystem (大文件缓存)
└── 音频文件
```

## 技术实现

### 1. 内存缓存 (L1)

```typescript
class MemoryCache {
  private cache = new Map<string, CacheItem>();
  private readonly maxSize = 50; // 最多缓存50个对象
  
  set<T>(key: string, value: T, ttl: number = 300000): void { // 5分钟TTL
    // LRU 清理策略
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    });
  }
  
  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;
    
    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value as T;
  }
  
  // 预热缓存
  warmup(data: Record<string, any>): void {
    Object.entries(data).forEach(([key, value]) => {
      this.set(key, value);
    });
  }
}

// 全局内存缓存实例
export const memoryCache = new MemoryCache();
```

### 2. AsyncStorage 缓存 (L2)

```typescript
class AsyncStorageCache {
  private readonly prefix = 'dokkai_';
  
  async set(key: string, value: any, ttl?: number): Promise<void> {
    const cacheItem = {
      value,
      timestamp: Date.now(),
      ttl
    };
    
    await AsyncStorage.setItem(
      `${this.prefix}${key}`, 
      JSON.stringify(cacheItem)
    );
  }
  
  async get<T>(key: string): Promise<T | null> {
    try {
      const cached = await AsyncStorage.getItem(`${this.prefix}${key}`);
      if (!cached) return null;
      
      const item = JSON.parse(cached);
      
      // 检查过期
      if (item.ttl && Date.now() - item.timestamp > item.ttl) {
        await this.remove(key);
        return null;
      }
      
      return item.value as T;
    } catch (error) {
      console.error('AsyncStorage get error:', error);
      return null;
    }
  }
  
  async remove(key: string): Promise<void> {
    await AsyncStorage.removeItem(`${this.prefix}${key}`);
  }
  
  // 批量操作
  async multiSet(items: Array<[string, any]>): Promise<void> {
    const pairs = items.map(([key, value]) => [
      `${this.prefix}${key}`,
      JSON.stringify({
        value,
        timestamp: Date.now()
      })
    ]);
    
    await AsyncStorage.multiSet(pairs);
  }
}

export const asyncCache = new AsyncStorageCache();
```

### 3. SQLite 缓存 (L3)

```typescript
import * as SQLite from 'expo-sqlite';

class SQLiteCache {
  private db: SQLite.WebSQLDatabase;
  
  constructor() {
    this.db = SQLite.openDatabase('dokkai_cache.db');
    this.initTables();
  }
  
  private initTables(): void {
    this.db.transaction(tx => {
      // 文章元数据表
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS articles_cache (
          id TEXT PRIMARY KEY,
          title TEXT,
          category TEXT,
          tags TEXT,
          difficulty TEXT,
          sentence_count INTEGER,
          cached_at INTEGER,
          data TEXT
        )
      `);
      
      // 音频元数据表
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS audio_cache (
          id TEXT PRIMARY KEY,
          article_id TEXT,
          level TEXT,
          sentence_index INTEGER,
          local_path TEXT,
          file_size INTEGER,
          cached_at INTEGER
        )
      `);
      
      // 创建索引
      tx.executeSql('CREATE INDEX IF NOT EXISTS idx_articles_category ON articles_cache(category)');
      tx.executeSql('CREATE INDEX IF NOT EXISTS idx_audio_article ON audio_cache(article_id, level)');
    });
  }
  
  // 缓存文章
  async cacheArticle(article: Article): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db.transaction(tx => {
        tx.executeSql(
          `INSERT OR REPLACE INTO articles_cache 
           (id, title, category, tags, difficulty, sentence_count, cached_at, data) 
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            article.id,
            article.title,
            article.category,
            JSON.stringify(article.tags),
            article.difficulty,
            article.sentenceCount,
            Date.now(),
            JSON.stringify(article)
          ],
          () => resolve(),
          (_, error) => {
            reject(error);
            return false;
          }
        );
      });
    });
  }
  
  // 获取缓存的文章
  async getCachedArticle(id: string): Promise<Article | null> {
    return new Promise((resolve) => {
      this.db.transaction(tx => {
        tx.executeSql(
          'SELECT data FROM articles_cache WHERE id = ?',
          [id],
          (_, result) => {
            if (result.rows.length > 0) {
              const article = JSON.parse(result.rows.item(0).data);
              resolve(article);
            } else {
              resolve(null);
            }
          },
          () => {
            resolve(null);
            return false;
          }
        );
      });
    });
  }
  
  // 获取缓存的文章列表
  async getCachedArticles(category?: string, limit: number = 20): Promise<Article[]> {
    return new Promise((resolve) => {
      this.db.transaction(tx => {
        const query = category 
          ? 'SELECT data FROM articles_cache WHERE category = ? ORDER BY cached_at DESC LIMIT ?'
          : 'SELECT data FROM articles_cache ORDER BY cached_at DESC LIMIT ?';
        const params = category ? [category, limit] : [limit];
        
        tx.executeSql(
          query,
          params,
          (_, result) => {
            const articles = [];
            for (let i = 0; i < result.rows.length; i++) {
              articles.push(JSON.parse(result.rows.item(i).data));
            }
            resolve(articles);
          },
          () => {
            resolve([]);
            return false;
          }
        );
      });
    });
  }
}

export const sqliteCache = new SQLiteCache();
```

### 4. 文件系统缓存 (L4)

```typescript
import * as FileSystem from 'expo-file-system';

class FileSystemCache {
  private readonly cacheDir = `${FileSystem.documentDirectory}dokkai_cache/`;
  private readonly audioDir = `${this.cacheDir}audio/`;
  
  constructor() {
    this.ensureDirectories();
  }
  
  private async ensureDirectories(): Promise<void> {
    const dirInfo = await FileSystem.getInfoAsync(this.audioDir);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(this.audioDir, { intermediates: true });
    }
  }
  
  // 下载并缓存音频
  async downloadAudio(
    audioUrl: string, 
    articleId: string, 
    level: string, 
    sentenceIndex: number
  ): Promise<string> {
    const fileName = `${articleId}_${level}_${sentenceIndex}.mp3`;
    const localPath = `${this.audioDir}${fileName}`;
    
    // 检查是否已存在
    const fileInfo = await FileSystem.getInfoAsync(localPath);
    if (fileInfo.exists) {
      return localPath;
    }
    
    try {
      // 下载文件
      const downloadResult = await FileSystem.downloadAsync(audioUrl, localPath);
      
      // 记录到 SQLite
      await this.recordAudioCache(articleId, level, sentenceIndex, localPath, fileInfo.size || 0);
      
      return downloadResult.uri;
    } catch (error) {
      console.error('Audio download failed:', error);
      throw error;
    }
  }
  
  // 获取本地音频路径
  async getLocalAudioPath(
    articleId: string, 
    level: string, 
    sentenceIndex: number
  ): Promise<string | null> {
    const fileName = `${articleId}_${level}_${sentenceIndex}.mp3`;
    const localPath = `${this.audioDir}${fileName}`;
    
    const fileInfo = await FileSystem.getInfoAsync(localPath);
    return fileInfo.exists ? localPath : null;
  }
  
  // 预加载音频
  async preloadAudio(
    articleId: string, 
    level: string, 
    sentences: string[]
  ): Promise<void> {
    const downloadPromises = sentences.map(async (_, index) => {
      try {
        const audioUrl = await this.getAudioUrl(articleId, level, index);
        if (audioUrl) {
          await this.downloadAudio(audioUrl, articleId, level, index);
        }
      } catch (error) {
        console.warn(`Failed to preload audio ${articleId}/${level}/${index}:`, error);
      }
    });
    
    // 并行下载，但不等待全部完成
    Promise.allSettled(downloadPromises);
  }
  
  // 清理缓存
  async cleanup(maxSizeMB: number = 500): Promise<void> {
    const cacheSize = await this.getCacheSize();
    
    if (cacheSize > maxSizeMB * 1024 * 1024) {
      // 获取所有音频文件，按访问时间排序
      const files = await FileSystem.readDirectoryAsync(this.audioDir);
      const fileInfos = await Promise.all(
        files.map(async (file) => {
          const path = `${this.audioDir}${file}`;
          const info = await FileSystem.getInfoAsync(path);
          return { path, ...info };
        })
      );
      
      // 按修改时间排序，删除最旧的文件
      fileInfos
        .sort((a, b) => (a.modificationTime || 0) - (b.modificationTime || 0))
        .slice(0, Math.floor(files.length * 0.3)) // 删除30%最旧的文件
        .forEach(async (file) => {
          await FileSystem.deleteAsync(file.path);
        });
    }
  }
  
  private async getCacheSize(): Promise<number> {
    try {
      const files = await FileSystem.readDirectoryAsync(this.audioDir);
      let totalSize = 0;
      
      for (const file of files) {
        const info = await FileSystem.getInfoAsync(`${this.audioDir}${file}`);
        totalSize += info.size || 0;
      }
      
      return totalSize;
    } catch {
      return 0;
    }
  }
  
  private async recordAudioCache(
    articleId: string,
    level: string,
    sentenceIndex: number,
    localPath: string,
    fileSize: number
  ): Promise<void> {
    // 记录到 SQLite 缓存
    const id = `${articleId}_${level}_${sentenceIndex}`;
    // 这里调用 SQLiteCache 的方法
  }
  
  private async getAudioUrl(
    articleId: string,
    level: string,
    sentenceIndex: number
  ): Promise<string | null> {
    // 从 API 获取音频 URL
    try {
      const response = await fetch(`/api/articles/${articleId}/audio/${level}/${sentenceIndex}`);
      const data = await response.json();
      return data.audioUrl;
    } catch {
      return null;
    }
  }
}

export const fileCache = new FileSystemCache();
```

## 统一缓存管理器

```typescript
class CacheManager {
  // 获取文章（智能缓存策略）
  async getArticle(id: string): Promise<Article | null> {
    // L1: 内存缓存
    let article = memoryCache.get<Article>(id);
    if (article) {
      console.log('Cache hit: Memory');
      return article;
    }
    
    // L3: SQLite 缓存
    article = await sqliteCache.getCachedArticle(id);
    if (article) {
      console.log('Cache hit: SQLite');
      memoryCache.set(id, article);
      return article;
    }
    
    // 网络请求
    console.log('Cache miss: Fetching from API');
    try {
      const response = await fetch(`/api/articles/${id}`);
      article = await response.json();
      
      if (article) {
        // 缓存到各层
        memoryCache.set(id, article);
        await sqliteCache.cacheArticle(article);
        
        // 预加载音频（后台进行）
        this.preloadArticleAudio(article);
      }
      
      return article;
    } catch (error) {
      console.error('Failed to fetch article:', error);
      return null;
    }
  }
  
  // 获取文章列表
  async getArticles(category?: string, limit: number = 20): Promise<Article[]> {
    const cacheKey = `articles_${category || 'all'}_${limit}`;
    
    // L1: 内存缓存
    let articles = memoryCache.get<Article[]>(cacheKey);
    if (articles) {
      return articles;
    }
    
    // L3: SQLite 缓存
    articles = await sqliteCache.getCachedArticles(category, limit);
    if (articles.length > 0) {
      memoryCache.set(cacheKey, articles, 300000); // 5分钟缓存
      return articles;
    }
    
    // 网络请求
    try {
      const url = `/api/articles?limit=${limit}${category ? `&category=${category}` : ''}`;
      const response = await fetch(url);
      const data = await response.json();
      articles = data.articles;
      
      // 缓存到各层
      memoryCache.set(cacheKey, articles, 300000);
      
      // 批量缓存到 SQLite
      for (const article of articles) {
        await sqliteCache.cacheArticle(article);
      }
      
      return articles;
    } catch (error) {
      console.error('Failed to fetch articles:', error);
      return [];
    }
  }
  
  // 获取音频（支持离线播放）
  async getAudioPath(
    articleId: string,
    level: string,
    sentenceIndex: number
  ): Promise<string | null> {
    // 先检查本地缓存
    const localPath = await fileCache.getLocalAudioPath(articleId, level, sentenceIndex);
    if (localPath) {
      return localPath;
    }
    
    // 本地没有，从网络下载
    try {
      const response = await fetch(`/api/articles/${articleId}/audio/${level}/${sentenceIndex}`);
      const data = await response.json();
      
      if (data.audioUrl) {
        return await fileCache.downloadAudio(data.audioUrl, articleId, level, sentenceIndex);
      }
    } catch (error) {
      console.error('Failed to get audio:', error);
    }
    
    return null;
  }
  
  // 预加载文章音频
  private async preloadArticleAudio(article: Article): Promise<void> {
    // 只预加载前3句的N5级别音频
    const sentences = article.jlptData.N5?.sentences?.slice(0, 3) || [];
    if (sentences.length > 0) {
      fileCache.preloadAudio(article.id, 'N5', sentences);
    }
  }
  
  // 智能预加载策略
  async smartPreload(currentArticleId: string): Promise<void> {
    // 预加载下一篇文章
    const articles = await this.getArticles();
    const currentIndex = articles.findIndex(a => a.id === currentArticleId);
    
    if (currentIndex >= 0 && currentIndex < articles.length - 1) {
      const nextArticle = articles[currentIndex + 1];
      // 后台预加载
      setTimeout(() => {
        this.getArticle(nextArticle.id);
      }, 1000);
    }
  }
}

export const cacheManager = new CacheManager();
```

## 预加载策略

### 1. 启动预加载

```typescript
// App 启动时预加载热门内容
class AppPreloader {
  async preloadOnStartup(): Promise<void> {
    try {
      // 预加载文章列表
      await cacheManager.getArticles(undefined, 10);
      
      // 预加载用户最近学习的文章
      const recentArticles = await this.getRecentArticles();
      for (const articleId of recentArticles.slice(0, 3)) {
        cacheManager.getArticle(articleId);
      }
    } catch (error) {
      console.warn('Preload failed:', error);
    }
  }
  
  private async getRecentArticles(): Promise<string[]> {
    const progress = await asyncCache.get<Record<string, any>>('user_progress');
    if (!progress) return [];
    
    return Object.keys(progress)
      .sort((a, b) => (progress[b].lastAccessed || 0) - (progress[a].lastAccessed || 0))
      .slice(0, 5);
  }
}
```

### 2. 用户行为预测

```typescript
class BehaviorPredictor {
  // 用户开始学习时的预加载
  async onArticleStart(articleId: string, level: string): Promise<void> {
    const article = await cacheManager.getArticle(articleId);
    if (!article) return;
    
    // 预加载当前级别的前5句音频
    const sentences = article.jlptData[level]?.sentences?.slice(0, 5) || [];
    fileCache.preloadAudio(articleId, level, sentences);
    
    // 预加载下一篇文章信息
    cacheManager.smartPreload(articleId);
  }
  
  // 用户切换句子时的预加载
  async onSentenceChange(
    articleId: string, 
    level: string, 
    currentIndex: number
  ): Promise<void> {
    // 预加载下一句和下下句的音频
    const nextIndices = [currentIndex + 1, currentIndex + 2];
    
    for (const index of nextIndices) {
      cacheManager.getAudioPath(articleId, level, index);
    }
  }
}

export const behaviorPredictor = new BehaviorPredictor();
```

## 缓存清理和管理

### 1. 存储监控

```typescript
class StorageMonitor {
  async getStorageInfo(): Promise<StorageInfo> {
    const asyncStorageSize = await this.getAsyncStorageSize();
    const sqliteSize = await this.getSQLiteSize();
    const audioSize = await this.getAudioCacheSize();
    
    return {
      asyncStorage: asyncStorageSize,
      sqlite: sqliteSize,
      audio: audioSize,
      total: asyncStorageSize + sqliteSize + audioSize
    };
  }
  
  private async getAsyncStorageSize(): Promise<number> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const dokkaiKeys = keys.filter(key => key.startsWith('dokkai_'));
      
      let totalSize = 0;
      for (const key of dokkaiKeys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          totalSize += new Blob([value]).size;
        }
      }
      
      return totalSize;
    } catch {
      return 0;
    }
  }
  
  private async getSQLiteSize(): Promise<number> {
    // SQLite 文件大小检查
    const dbPath = `${FileSystem.documentDirectory}SQLite/dokkai_cache.db`;
    const info = await FileSystem.getInfoAsync(dbPath);
    return info.exists ? (info.size || 0) : 0;
  }
  
  private async getAudioCacheSize(): Promise<number> {
    return await fileCache.getCacheSize();
  }
}
```

### 2. 智能清理策略

```typescript
class CacheCleaner {
  async performCleanup(targetSizeMB: number = 300): Promise<void> {
    const storageInfo = await new StorageMonitor().getStorageInfo();
    const targetSize = targetSizeMB * 1024 * 1024;
    
    if (storageInfo.total <= targetSize) {
      return; // 不需要清理
    }
    
    console.log('Starting cache cleanup...');
    
    // 1. 清理过期的 AsyncStorage 数据
    await this.cleanExpiredAsyncStorage();
    
    // 2. 清理旧的音频文件
    await fileCache.cleanup(targetSizeMB * 0.7); // 音频占70%
    
    // 3. 清理 SQLite 中的旧数据
    await this.cleanOldSQLiteData();
    
    console.log('Cache cleanup completed');
  }
  
  private async cleanExpiredAsyncStorage(): Promise<void> {
    const keys = await AsyncStorage.getAllKeys();
    const dokkaiKeys = keys.filter(key => key.startsWith('dokkai_'));
    
    for (const key of dokkaiKeys) {
      const value = await AsyncStorage.getItem(key);
      if (value) {
        try {
          const item = JSON.parse(value);
          if (item.ttl && Date.now() - item.timestamp > item.ttl) {
            await AsyncStorage.removeItem(key);
          }
        } catch {
          // 删除损坏的数据
          await AsyncStorage.removeItem(key);
        }
      }
    }
  }
  
  private async cleanOldSQLiteData(): Promise<void> {
    // 删除30天前的缓存数据
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    
    sqliteCache.db.transaction(tx => {
      tx.executeSql(
        'DELETE FROM articles_cache WHERE cached_at < ?',
        [thirtyDaysAgo]
      );
      tx.executeSql(
        'DELETE FROM audio_cache WHERE cached_at < ?',
        [thirtyDaysAgo]
      );
    });
  }
}
```

## 用户控制选项

### 缓存设置界面

```typescript
interface CacheSettings {
  enableAudioCache: boolean;
  maxCacheSizeMB: number;
  preloadOnWiFiOnly: boolean;
  autoCleanup: boolean;
}

class CacheSettingsManager {
  private readonly SETTINGS_KEY = 'cache_settings';
  
  async getSettings(): Promise<CacheSettings> {
    const settings = await asyncCache.get<CacheSettings>(this.SETTINGS_KEY);
    return settings || {
      enableAudioCache: true,
      maxCacheSizeMB: 500,
      preloadOnWiFiOnly: true,
      autoCleanup: true
    };
  }
  
  async updateSettings(settings: Partial<CacheSettings>): Promise<void> {
    const current = await this.getSettings();
    const updated = { ...current, ...settings };
    await asyncCache.set(this.SETTINGS_KEY, updated);
  }
  
  async clearAllCache(): Promise<void> {
    // 清理所有缓存
    memoryCache.clear();
    await AsyncStorage.clear();
    await fileCache.cleanup(0);
    
    // 重新创建数据库
    await sqliteCache.recreateDatabase();
  }
}
```

## 性能监控

### 缓存命中率统计

```typescript
class CacheMetrics {
  private metrics = {
    memoryHits: 0,
    sqliteHits: 0,
    networkRequests: 0,
    audioLocalHits: 0,
    audioDownloads: 0
  };
  
  recordMemoryHit(): void {
    this.metrics.memoryHits++;
  }
  
  recordSQLiteHit(): void {
    this.metrics.sqliteHits++;
  }
  
  recordNetworkRequest(): void {
    this.metrics.networkRequests++;
  }
  
  getHitRate(): number {
    const totalRequests = this.metrics.memoryHits + 
                         this.metrics.sqliteHits + 
                         this.metrics.networkRequests;
    
    if (totalRequests === 0) return 0;
    
    const hits = this.metrics.memoryHits + this.metrics.sqliteHits;
    return (hits / totalRequests) * 100;
  }
  
  getReport(): string {
    return `
    Cache Hit Rate: ${this.getHitRate().toFixed(1)}%
    Memory Hits: ${this.metrics.memoryHits}
    SQLite Hits: ${this.metrics.sqliteHits}
    Network Requests: ${this.metrics.networkRequests}
    Audio Local Hits: ${this.metrics.audioLocalHits}
    Audio Downloads: ${this.metrics.audioDownloads}
    `;
  }
}

export const cacheMetrics = new CacheMetrics();
```

## 总结

这个客户端缓存策略能够：

1. **大幅减少网络请求**: 通过多层缓存和智能预加载
2. **提供离线能力**: 本地存储完整的学习内容
3. **优化用户体验**: 近即时的内容访问和音频播放
4. **智能存储管理**: 自动清理和用户控制选项
5. **性能监控**: 缓存效率的实时监控

配合 Cloudflare Worker 的边缘计算能力，这套缓存策略可以将冷启动延迟的影响降到最低，为用户提供流畅的学习体验。