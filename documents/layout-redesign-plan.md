# Dokkai App 布局重新设计规划

## 项目概述

本文档详细规划了 Dokkai 应用的布局重新设计，主要目标是：
1. 去掉 tab bar，改为上一句/下一句操作按钮
2. 重新设计 header bar，包含文章标题、菜单和级别选择
3. 实现文章切换功能
4. 创建文章数据结构和管理系统

## 当前架构分析

### 现有结构
- **导航**: 使用 Expo Router 的 Tabs 导航（`app/_layout.tsx`）
- **主页面**: `app/index.tsx` 渲染 `JLPTLearning` 组件
- **数据结构**: 单一 JLPT 数据文件（`data/jlptData.ts`）
- **组件**: 模块化组件设计，包含 LevelCard、NavigationControls 等

### 现有功能
- 句子级别对比学习（N5-N1）
- 进度跟踪
- 完整文章查看模态框
- 级别访问栏

## 设计目标

### 1. 新的布局结构
```
┌─────────────────────────────────────┐
│ Header Bar                          │
│ [≡] 文章标题        [级别选择 ▼]    │
├─────────────────────────────────────┤
│                                     │
│         主要内容区域                │
│      (Level Cards)                  │
│                                     │
├─────────────────────────────────────┤
│     [← 上一句]  [下一句 →]         │
└─────────────────────────────────────┘
```

### 2. 数据结构重新设计
- 创建 `data/articles/` 目录存放文章
- 创建 `data/index.json` 管理文章索引
- 每篇文章独立的 JSON 文件
- 文章元数据管理

## 详细任务分解

### Phase 1: 数据结构重构 (预计 2-3 小时)

#### Task 1.1: 创建文章数据结构
- [ ] 设计文章数据接口 (`types/article.ts`)
- [ ] 创建 `data/articles/` 目录
- [ ] 设计文章索引结构 (`data/index.json`)
- [ ] 定义文章元数据格式

#### Task 1.2: 重构现有数据
- [ ] 将现有 JLPT 数据转换为第一篇文章
- [ ] 创建文章标题和描述
- [ ] 实现数据加载服务

#### Task 1.3: 创建额外文章
- [ ] 基于现有模式创建第二篇文章
- [ ] 创建第三篇文章
- [ ] 确保数据一致性

### Phase 2: 导航结构重构 (预计 1-2 小时)

#### Task 2.1: 移除 Tab 导航
- [ ] 修改 `app/_layout.tsx`，从 Tabs 改为 Stack
- [ ] 移除 `app/me.tsx` 页面
- [ ] 更新路由配置

#### Task 2.2: 创建新的 Header 组件
- [ ] 设计 `components/AppHeader.tsx`
- [ ] 实现三横线菜单按钮
- [ ] 实现文章标题显示
- [ ] 实现级别选择下拉菜单

### Phase 3: 主要功能实现 (预计 3-4 小时)

#### Task 3.1: 文章切换功能
- [ ] 创建 `components/ArticleSelector.tsx`
- [ ] 实现文章列表显示
- [ ] 实现文章切换逻辑
- [ ] 更新状态管理

#### Task 3.2: 导航控制重构
- [ ] 修改 `components/NavigationControls.tsx`
- [ ] 移除级别访问栏
- [ ] 优化上一句/下一句按钮样式
- [ ] 实现底部固定布局

#### Task 3.3: 级别选择功能
- [ ] 创建 `components/LevelSelector.tsx`
- [ ] 实现下拉选择界面
- [ ] 集成到 Header 中
- [ ] 处理级别切换逻辑

### Phase 4: 状态管理优化 (预计 1-2 小时)

#### Task 4.1: Hook 重构
- [ ] 更新 `hooks/useJLPTData.ts`
- [ ] 添加文章管理功能
- [ ] 实现文章切换状态
- [ ] 优化性能

#### Task 4.2: 数据服务更新
- [ ] 更新数据加载逻辑
- [ ] 实现文章缓存
- [ ] 错误处理优化

### Phase 5: UI/UX 优化 (预计 1-2 小时)

#### Task 5.1: 样式调整
- [ ] 更新主题配置
- [ ] 优化组件样式
- [ ] 确保响应式设计
- [ ] 添加过渡动画

#### Task 5.2: 用户体验优化
- [ ] 添加加载状态
- [ ] 优化交互反馈
- [ ] 测试各种屏幕尺寸

## 技术实现细节

### 新的数据结构

```typescript
// types/article.ts
export interface Article {
  id: string;
  title: string;
  description: string;
  author?: string;
  createdAt: string;
  tags: string[];
  jlptData: JLPTData;
}

export interface ArticleIndex {
  articles: ArticleMetadata[];
  currentArticle: string;
}

export interface ArticleMetadata {
  id: string;
  title: string;
  description: string;
  tags: string[];
  createdAt: string;
}
```

### 文件结构
```
data/
├── index.json              # 文章索引
├── articles/
│   ├── daily-life.json     # 日常生活文章
│   ├── school-life.json    # 学校生活文章
│   └── travel.json         # 旅行文章
└── jlptData.ts            # 保留作为类型定义
```

### 组件层次结构
```
App
├── AppHeader
│   ├── MenuButton (≡)
│   ├── ArticleTitle
│   └── LevelSelector
├── JLPTLearning (主要内容)
│   └── LevelCards
└── NavigationControls (底部固定)
    ├── PreviousButton
    └── NextButton
```

## 风险评估

### 技术风险
- **数据迁移复杂性**: 中等风险，需要仔细处理现有数据
- **状态管理复杂度**: 低风险，现有架构支持良好
- **性能影响**: 低风险，数据量不大

### 用户体验风险
- **学习曲线**: 低风险，界面更直观
- **功能丢失**: 无风险，所有功能都会保留

## 成功标准

1. **功能完整性**: 所有现有功能正常工作
2. **新功能**: 文章切换、级别选择正常工作
3. **性能**: 应用响应速度不降低
4. **用户体验**: 界面更简洁直观
5. **代码质量**: 保持现有代码质量标准

## 时间估算

- **总预计时间**: 8-13 小时
- **关键路径**: 数据结构重构 → 导航重构 → 功能实现
- **并行任务**: UI 优化可与功能实现并行进行

## 下一步行动

1. 确认设计方案
2. 开始 Phase 1: 数据结构重构
3. 逐步实现各个阶段
4. 持续测试和优化
