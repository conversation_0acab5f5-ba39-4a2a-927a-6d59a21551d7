# API 设计规范

## 概述

本文档详细定义了 Dokkai 日语学习应用的 API 接口设计，包括数据结构、端点定义、音频处理和逐词高亮功能。

## 数据结构定义

### 核心数据类型

#### Article (文章元数据)
```typescript
interface Article {
  id: string;                    // 文章唯一标识
  title: string;                 // 文章标题（日语）
  description: string;           // 文章描述（日语）
  author: string;                // 作者
  createdAt: string;            // 创建时间 (ISO 8601)
  tags: string[];               // 标签数组（日语）
  category: string;             // 分类：daily-life, school-life, travel, social-life, culture, work-life
  difficulty: string;           // 难度：beginner, intermediate, advanced
  estimatedReadingTime: number; // 预估阅读时间（分钟）
  sentenceCount: number;        // 句子总数
  jlptData: JLPTData;          // JLPT 各级别数据
}
```

#### JLPTData (JLPT 级别数据)
```typescript
interface JLPTData {
  [key in Level]: LevelData;
}

type Level = 'N5' | 'N4' | 'N3' | 'N2' | 'N1';

interface LevelData {
  sentences: string[];           // 句子数组
  grammar_features: string[];    // 语法特征说明
  full_text: string;            // 完整文本
  grammar_points: string[];     // 语法要点
  word_count: number;           // 字数统计
  description: string;          // 级别描述
  audio_metadata?: AudioMetadata[]; // 音频元数据（可选）
}
```

#### AudioMetadata (音频元数据)
```typescript
interface AudioMetadata {
  sentence_index: number;       // 句子索引
  audio_path: string;          // 音频文件路径
  duration: number;            // 音频时长（秒）
  file_size: number;           // 文件大小（字节）
  word_timings: WordTiming[];  // 逐词时间戳
}

interface WordTiming {
  word: string;                // 词汇
  start_time: number;          // 开始时间（秒）
  end_time: number;            // 结束时间（秒）
  reading?: string;            // 读音（假名）
  part_of_speech?: string;     // 词性
}
```

### 本地数据存储格式

#### data/index.json 结构
```json
{
  "version": "1.0.0",
  "lastUpdated": "2025-07-22T00:00:00.000Z", 
  "currentArticle": "daily-life-student",
  "articles": [
    {
      "id": "daily-life-student",
      "title": "大学生の日常生活",
      "description": "大学生の朝から夜までの一日を描いたシンプルなストーリー。日常生活の基本を学びましょう。",
      "author": "Dokkai Team",
      "createdAt": "2025-01-22T00:00:00.000Z",
      "tags": ["日常生活", "大学生", "基礎会話"],
      "category": "daily-life",
      "difficulty": "beginner",
      "estimatedReadingTime": 5,
      "sentenceCount": 10
    }
  ]
}
```

#### data/articles/{id}.json 结构
```json
{
  "id": "daily-life-student",
  "title": "大学生の日常生活",
  "description": "大学生の朝から夜までの一日を描いたシンプルなストーリー。日常生活の基本を学びましょう。",
  "author": "Dokkai Team", 
  "createdAt": "2025-01-22T00:00:00.000Z",
  "tags": ["日常生活", "大学生", "基礎会話"],
  "category": "daily-life",
  "difficulty": "beginner",
  "estimatedReadingTime": 5,
  "sentenceCount": 10,
  "jlptData": {
    "N5": {
      "sentences": [
        "私は田中です。大学生です。",
        "毎朝七時に起きます。",
        "朝ごはんを食べます。"
      ],
      "grammar_features": [
        "基本自己介绍",
        "日常作息表达",
        "基础动词活用"
      ],
      "full_text": "私は田中です。大学生です。毎朝七時に起きます。朝ごはんを食べます。",
      "grammar_points": [
        "基本助词(は/が/を)",
        "基础动词ます形",
        "时间表达"
      ],
      "word_count": 45,
      "description": "入门级：简单句式，基础语法",
      "audio_metadata": [
        {
          "sentence_index": 0,
          "audio_path": "audio/daily-life-student/N5/0.mp3",
          "duration": 3.2,
          "file_size": 51200,
          "word_timings": [
            {
              "word": "私",
              "start_time": 0.0,
              "end_time": 0.5,
              "reading": "わたし",
              "part_of_speech": "代名詞"
            },
            {
              "word": "は",
              "start_time": 0.5,
              "end_time": 0.7,
              "reading": "は",
              "part_of_speech": "助詞"
            },
            {
              "word": "田中",
              "start_time": 0.7,
              "end_time": 1.3,
              "reading": "たなか",
              "part_of_speech": "固有名詞"
            },
            {
              "word": "です",
              "start_time": 1.3,
              "end_time": 1.8,
              "reading": "です",
              "part_of_speech": "助動詞"
            }
          ]
        }
      ]
    },
    "N4": {
      "sentences": [
        "私は田中と申します。現在、大学で経済学を専攻しています。",
        "毎朝七時に起床し、朝食を取ります。"
      ],
      "grammar_features": [
        "敬语表达",
        "专业用语",
        "复合句式"
      ],
      "full_text": "私は田中と申します。現在、大学で経済学を専攻しています。毎朝七時に起床し、朝食を取ります。",
      "grammar_points": [
        "敬语形式(と申します)",
        "现在进行时",
        "连接助词(し)"
      ],
      "word_count": 62,
      "description": "初级：连接句式，基础复合表达",
      "audio_metadata": [
        {
          "sentence_index": 0,
          "audio_path": "audio/daily-life-student/N4/0.mp3",
          "duration": 4.8,
          "file_size": 76800,
          "word_timings": [
            {
              "word": "私",
              "start_time": 0.0,
              "end_time": 0.4,
              "reading": "わたし",
              "part_of_speech": "代名詞"
            },
            {
              "word": "は",
              "start_time": 0.4,
              "end_time": 0.6,
              "reading": "は",
              "part_of_speech": "助詞"
            },
            {
              "word": "田中",
              "start_time": 0.6,
              "end_time": 1.0,
              "reading": "たなか",
              "part_of_speech": "固有名詞"
            },
            {
              "word": "と",
              "start_time": 1.0,
              "end_time": 1.2,
              "reading": "と",
              "part_of_speech": "助詞"
            },
            {
              "word": "申します",
              "start_time": 1.2,
              "end_time": 2.0,
              "reading": "もうします",
              "part_of_speech": "動詞"
            }
          ]
        }
      ]
    }
  }
}
```

## 音频路径设计

### 约定路径 vs 绝对路径

**推荐使用约定路径策略：**

#### 路径约定规则
```
音频文件路径格式：audio/{article_id}/{level}/{sentence_index}.mp3
时间戳文件路径：audio/{article_id}/{level}/{sentence_index}.json

示例：
- audio/daily-life-student/N5/0.mp3
- audio/daily-life-student/N5/0.json
- audio/daily-life-student/N4/0.mp3
- audio/daily-life-student/N4/0.json
```

#### 优势分析
1. **统一性**: 路径规则简单明了
2. **可预测性**: 客户端可以根据规则生成路径
3. **缓存友好**: 便于实现本地缓存命名
4. **CDN 优化**: 便于设置缓存策略
5. **维护性**: 批量操作和管理更容易

#### 实现方式
```typescript
// 路径生成工具
class AudioPathGenerator {
  static getAudioPath(articleId: string, level: string, sentenceIndex: number): string {
    return `audio/${articleId}/${level}/${sentenceIndex}.mp3`;
  }
  
  static getTimingPath(articleId: string, level: string, sentenceIndex: number): string {
    return `audio/${articleId}/${level}/${sentenceIndex}.json`;
  }
  
  static getAudioUrl(baseUrl: string, articleId: string, level: string, sentenceIndex: number): string {
    return `${baseUrl}/${this.getAudioPath(articleId, level, sentenceIndex)}`;
  }
}
```

### 逐词时间戳文件格式

#### 单独的时间戳文件 (推荐)
```json
{
  "sentence": "私は田中です。大学生です。",
  "duration": 3.2,
  "word_timings": [
    {
      "word": "私",
      "start_time": 0.0,
      "end_time": 0.5,
      "reading": "わたし",
      "part_of_speech": "代名詞",
      "definition": "I, me"
    },
    {
      "word": "は", 
      "start_time": 0.5,
      "end_time": 0.7,
      "reading": "は",
      "part_of_speech": "助詞",
      "definition": "topic marker"
    },
    {
      "word": "田中",
      "start_time": 0.7,
      "end_time": 1.3,
      "reading": "たなか",
      "part_of_speech": "固有名詞",
      "definition": "Tanaka (surname)"
    },
    {
      "word": "です",
      "start_time": 1.3,
      "end_time": 1.8,
      "reading": "です",
      "part_of_speech": "助動詞",
      "definition": "polite copula"
    },
    {
      "word": "。",
      "start_time": 1.8,
      "end_time": 2.0,
      "reading": "",
      "part_of_speech": "記号",
      "definition": "period"
    },
    {
      "word": "大学生",
      "start_time": 2.0,
      "end_time": 2.8,
      "reading": "だいがくせい",
      "part_of_speech": "名詞",
      "definition": "university student"
    },
    {
      "word": "です",
      "start_time": 2.8,
      "end_time": 3.2,
      "reading": "です",
      "part_of_speech": "助動詞",
      "definition": "polite copula"
    }
  ]
}
```

## API 端点设计

### 基础端点

#### 1. 获取文章列表
```http
GET /api/articles
```

**查询参数:**
```typescript
interface ArticleListQuery {
  page?: number;          // 页码，默认 1
  limit?: number;         // 每页数量，默认 20，最大 100
  category?: string;      // 分类筛选
  difficulty?: string;    // 难度筛选
  tags?: string[];        // 标签筛选（数组）
  search?: string;        // 搜索关键词
  sort_by?: 'created_at' | 'title' | 'difficulty'; // 排序字段
  sort_order?: 'asc' | 'desc'; // 排序方向
}
```

**响应格式:**
```typescript
interface ArticleListResponse {
  articles: Article[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  filters: {
    categories: string[];    // 可用分类列表
    difficulties: string[];  // 可用难度列表
    tags: string[];         // 热门标签列表
  };
}
```

**示例请求:**
```http
GET /api/articles?category=daily-life&difficulty=beginner&limit=10&page=1
```

**示例响应:**
```json
{
  "articles": [
    {
      "id": "daily-life-student",
      "title": "大学生の日常生活",
      "description": "大学生の朝から夜までの一日を描いたシンプルなストーリー。",
      "author": "Dokkai Team",
      "createdAt": "2025-01-22T00:00:00.000Z",
      "tags": ["日常生活", "大学生", "基礎会話"],
      "category": "daily-life",
      "difficulty": "beginner",
      "estimatedReadingTime": 5,
      "sentenceCount": 10
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "total_pages": 3,
    "has_next": true,
    "has_prev": false
  },
  "filters": {
    "categories": ["daily-life", "school-life", "travel"],
    "difficulties": ["beginner", "intermediate", "advanced"],
    "tags": ["日常生活", "基礎会話", "大学生"]
  }
}
```

#### 2. 获取文章详情
```http
GET /api/articles/{id}
```

**路径参数:**
- `id`: 文章ID

**查询参数:**
```typescript
interface ArticleDetailQuery {
  include_audio?: boolean; // 是否包含音频元数据，默认 false
  levels?: Level[];        // 只返回指定级别的数据
}
```

**响应格式:**
```typescript
interface ArticleDetailResponse extends Article {
  // 继承 Article 的所有字段
}
```

**示例请求:**
```http
GET /api/articles/daily-life-student?include_audio=true&levels=N5,N4
```

#### 3. 获取音频信息
```http
GET /api/articles/{id}/audio/{level}/{sentence_index}
```

**路径参数:**
- `id`: 文章ID
- `level`: JLPT级别 (N5, N4, N3, N2, N1)
- `sentence_index`: 句子索引（从0开始）

**响应格式:**
```typescript
interface AudioResponse {
  audio_url: string;       // 音频文件URL
  timing_url: string;      // 时间戳文件URL
  duration: number;        // 音频时长
  file_size: number;       // 文件大小
  expires_at: string;      // URL过期时间 (ISO 8601)
}
```

**示例响应:**
```json
{
  "audio_url": "https://cdn.dokkai.com/audio/daily-life-student/N5/0.mp3?expires=1640995200&signature=abc123",
  "timing_url": "https://cdn.dokkai.com/audio/daily-life-student/N5/0.json?expires=1640995200&signature=def456", 
  "duration": 3.2,
  "file_size": 51200,
  "expires_at": "2025-01-01T12:00:00Z"
}
```

#### 4. 批量获取音频信息
```http
POST /api/articles/{id}/audio/batch
```

**请求体:**
```typescript
interface BatchAudioRequest {
  requests: Array<{
    level: Level;
    sentence_index: number;
  }>;
}
```

**响应格式:**
```typescript
interface BatchAudioResponse {
  audio_data: Array<{
    level: Level;
    sentence_index: number;
    audio_url: string;
    timing_url: string;
    duration: number;
    file_size: number;
  }>;
  expires_at: string;
}
```

#### 5. 获取词汇时间戳
```http
GET /api/articles/{id}/timing/{level}/{sentence_index}
```

**响应格式:**
```typescript
interface TimingResponse {
  sentence: string;
  duration: number;
  word_timings: WordTiming[];
}
```

### 高级端点

#### 6. 文章统计信息
```http
GET /api/articles/stats
```

**响应格式:**
```typescript
interface ArticleStatsResponse {
  total_articles: number;
  by_category: Record<string, number>;
  by_difficulty: Record<string, number>;
  total_sentences: number;
  total_audio_duration: number; // 总音频时长（秒）
  recent_additions: Article[];   // 最近添加的文章
}
```

#### 7. 相关文章推荐
```http
GET /api/articles/{id}/related
```

**查询参数:**
```typescript
interface RelatedQuery {
  limit?: number;     // 返回数量，默认5
  same_difficulty?: boolean; // 是否限制同难度
}
```

#### 8. 搜索文章
```http
GET /api/search
```

**查询参数:**
```typescript
interface SearchQuery {
  q: string;          // 搜索关键词
  type?: 'title' | 'content' | 'tags' | 'all'; // 搜索范围
  level?: Level;      // 限制JLPT级别
  limit?: number;
  page?: number;
}
```

## 错误处理

### 标准错误格式
```typescript
interface ApiError {
  error: {
    code: string;         // 错误代码
    message: string;      // 错误消息
    details?: any;        // 详细错误信息
    timestamp: string;    // 错误时间戳
  };
}
```

### 常见错误代码
```typescript
const ERROR_CODES = {
  // 4xx 客户端错误
  ARTICLE_NOT_FOUND: 'ARTICLE_NOT_FOUND',
  INVALID_LEVEL: 'INVALID_LEVEL',
  INVALID_SENTENCE_INDEX: 'INVALID_SENTENCE_INDEX',
  MISSING_PARAMETER: 'MISSING_PARAMETER',
  INVALID_PARAMETER: 'INVALID_PARAMETER',
  
  // 5xx 服务器错误
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  AUDIO_GENERATION_FAILED: 'AUDIO_GENERATION_FAILED',
  STORAGE_ERROR: 'STORAGE_ERROR'
} as const;
```

### 错误响应示例
```json
{
  "error": {
    "code": "ARTICLE_NOT_FOUND",
    "message": "指定的文章不存在",
    "details": {
      "article_id": "non-existent-article"
    },
    "timestamp": "2025-01-01T12:00:00.000Z"
  }
}
```

## 音频播放和逐词高亮实现

### 客户端集成示例

#### 音频播放器组件
```typescript
interface AudioPlayerProps {
  articleId: string;
  level: Level;
  sentenceIndex: number;
  onWordHighlight: (word: string, timing: WordTiming) => void;
}

class AudioPlayer extends React.Component<AudioPlayerProps> {
  private audio: Audio;
  private wordTimings: WordTiming[];
  private currentWordIndex: number = 0;
  
  async componentDidMount() {
    // 获取音频和时间戳数据
    const audioData = await this.fetchAudioData();
    const timingData = await this.fetchTimingData();
    
    this.wordTimings = timingData.word_timings;
    this.setupAudio(audioData.audio_url);
  }
  
  private setupAudio(audioUrl: string) {
    this.audio = new Audio(audioUrl);
    
    // 监听播放进度
    this.audio.addEventListener('timeupdate', this.handleTimeUpdate);
  }
  
  private handleTimeUpdate = () => {
    const currentTime = this.audio.currentTime;
    
    // 找到当前应该高亮的词
    const currentWord = this.wordTimings.find(timing => 
      currentTime >= timing.start_time && currentTime <= timing.end_time
    );
    
    if (currentWord && this.props.onWordHighlight) {
      this.props.onWordHighlight(currentWord.word, currentWord);
    }
  };
  
  private async fetchAudioData() {
    const response = await fetch(
      `/api/articles/${this.props.articleId}/audio/${this.props.level}/${this.props.sentenceIndex}`
    );
    return await response.json();
  }
  
  private async fetchTimingData() {
    const response = await fetch(
      `/api/articles/${this.props.articleId}/timing/${this.props.level}/${this.props.sentenceIndex}`
    );
    return await response.json();
  }
}
```

#### 文本高亮组件
```typescript
interface HighlightTextProps {
  sentence: string;
  wordTimings: WordTiming[];
  currentWord?: string;
}

const HighlightText: React.FC<HighlightTextProps> = ({ 
  sentence, 
  wordTimings, 
  currentWord 
}) => {
  return (
    <View style={styles.textContainer}>
      {wordTimings.map((timing, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.word,
            timing.word === currentWord && styles.highlightedWord
          ]}
          onPress={() => {
            // 点击词汇跳转到对应时间
            audio.currentTime = timing.start_time;
          }}
        >
          <Text style={[
            styles.wordText,
            timing.word === currentWord && styles.highlightedText
          ]}>
            {timing.word}
          </Text>
          
          {/* 显示读音 */}
          {timing.reading && (
            <Text style={styles.reading}>
              {timing.reading}
            </Text>
          )}
        </TouchableOpacity>
      ))}
    </View>
  );
};
```

### 缓存策略

#### 音频和时间戳缓存
```typescript
class AudioTimingCache {
  // 缓存音频文件
  async cacheAudio(
    articleId: string, 
    level: string, 
    sentenceIndex: number
  ): Promise<string> {
    const audioPath = AudioPathGenerator.getAudioPath(articleId, level, sentenceIndex);
    const localPath = `${FileSystem.documentDirectory}${audioPath}`;
    
    // 检查本地缓存
    const fileInfo = await FileSystem.getInfoAsync(localPath);
    if (fileInfo.exists) {
      return localPath;
    }
    
    // 下载音频文件
    const audioData = await this.fetchAudioData(articleId, level, sentenceIndex);
    await FileSystem.downloadAsync(audioData.audio_url, localPath);
    
    return localPath;
  }
  
  // 缓存时间戳数据
  async cacheTimingData(
    articleId: string,
    level: string, 
    sentenceIndex: number
  ): Promise<WordTiming[]> {
    const cacheKey = `timing_${articleId}_${level}_${sentenceIndex}`;
    
    // 先检查内存缓存
    const cached = memoryCache.get<WordTiming[]>(cacheKey);
    if (cached) return cached;
    
    // 检查 AsyncStorage
    const stored = await asyncCache.get<WordTiming[]>(cacheKey);
    if (stored) {
      memoryCache.set(cacheKey, stored);
      return stored;
    }
    
    // 从网络获取
    const timingData = await this.fetchTimingData(articleId, level, sentenceIndex);
    
    // 缓存到各层
    memoryCache.set(cacheKey, timingData.word_timings);
    await asyncCache.set(cacheKey, timingData.word_timings);
    
    return timingData.word_timings;
  }
}
```

## 数据验证

### 输入验证
```typescript
// JLPT级别验证
const VALID_LEVELS = ['N5', 'N4', 'N3', 'N2', 'N1'] as const;

function isValidLevel(level: string): level is Level {
  return VALID_LEVELS.includes(level as Level);
}

// 文章ID验证
function isValidArticleId(id: string): boolean {
  return /^[a-z0-9-]+$/.test(id) && id.length >= 3 && id.length <= 50;
}

// 句子索引验证
function isValidSentenceIndex(index: number, maxIndex: number): boolean {
  return Number.isInteger(index) && index >= 0 && index < maxIndex;
}
```

### 数据完整性检查
```typescript
function validateArticleData(article: Article): ValidationResult {
  const errors: string[] = [];
  
  // 必填字段检查
  if (!article.id) errors.push('Missing article id');
  if (!article.title) errors.push('Missing article title');
  if (!article.jlptData) errors.push('Missing JLPT data');
  
  // JLPT数据检查
  for (const level of VALID_LEVELS) {
    const levelData = article.jlptData[level];
    if (!levelData) continue;
    
    if (!levelData.sentences || levelData.sentences.length === 0) {
      errors.push(`Missing sentences for level ${level}`);
    }
    
    if (levelData.sentences.length !== levelData.grammar_features.length) {
      errors.push(`Sentence and grammar feature count mismatch for level ${level}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
```

## 性能优化

### 响应压缩
```typescript
// 启用 gzip 压缩
app.use(compression());

// 对于大型响应，使用流式传输
app.get('/api/articles/:id', async (req, res) => {
  const article = await getArticle(req.params.id);
  
  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Content-Encoding', 'gzip');
  
  const compressed = zlib.gzipSync(JSON.stringify(article));
  res.send(compressed);
});
```

### 缓存头设置
```typescript
// 设置适当的缓存头
const CACHE_DURATIONS = {
  articles: 3600,      // 文章列表缓存1小时
  article_detail: 86400, // 文章详情缓存24小时
  audio_metadata: 604800, // 音频元数据缓存7天
  timing_data: 604800    // 时间戳数据缓存7天
};

function setCacheHeaders(res: Response, type: keyof typeof CACHE_DURATIONS) {
  const duration = CACHE_DURATIONS[type];
  res.setHeader('Cache-Control', `public, max-age=${duration}`);
  res.setHeader('ETag', generateETag(/* data */));
}
```

## 总结

这个 API 设计具备以下特点：

1. **完整的数据结构**: 支持多级别内容和音频时间戳
2. **灵活的路径约定**: 统一的文件路径规则，便于缓存和管理
3. **逐词高亮支持**: 详细的时间戳数据支持精确的文本同步
4. **丰富的查询选项**: 支持分页、筛选、搜索等功能
5. **错误处理机制**: 标准化的错误响应格式
6. **性能优化**: 缓存策略和压缩支持
7. **数据验证**: 完整的输入验证和数据完整性检查

这个设计能够很好地支持 Dokkai 应用的所有功能需求，特别是音频播放和逐词高亮的核心功能。