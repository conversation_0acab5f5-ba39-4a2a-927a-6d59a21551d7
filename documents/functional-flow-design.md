# Dokkai App 功能流程设计

## 核心用户流程

### 1. 应用启动流程

```mermaid
graph TD
    A[应用启动] --> B[加载文章索引]
    B --> C{索引加载成功?}
    C -->|是| D[加载当前文章数据]
    C -->|否| E[显示错误状态]
    D --> F{文章数据加载成功?}
    F -->|是| G[显示主界面]
    F -->|否| H[显示加载失败]
    E --> I[重试按钮]
    H --> I
    I --> B
```

### 2. 文章切换流程

```mermaid
graph TD
    A[用户点击菜单按钮] --> B[显示文章选择器]
    B --> C[用户选择文章]
    C --> D[保存当前进度]
    D --> E[加载新文章数据]
    E --> F{加载成功?}
    F -->|是| G[更新界面]
    F -->|否| H[显示错误提示]
    G --> I[重置句子索引为0]
    H --> J[保持当前文章]
```

### 3. 句子导航流程

```mermaid
graph TD
    A[用户操作] --> B{操作类型}
    B -->|下一句| C{是否为最后一句?}
    B -->|上一句| D{是否为第一句?}
    C -->|否| E[句子索引+1]
    C -->|是| F[保持当前索引]
    D -->|否| G[句子索引-1]
    D -->|是| H[保持当前索引]
    E --> I[更新所有级别卡片]
    G --> I
    F --> J[显示提示信息]
    H --> J
    I --> K[更新进度条]
    K --> L[保存进度]
```

### 4. 级别选择流程

```mermaid
graph TD
    A[用户点击级别选择器] --> B[显示级别下拉菜单]
    B --> C[用户选择级别]
    C --> D[显示该级别完整文章]
    D --> E[用户可以关闭模态框]
    E --> F[返回主界面]
```

## 数据流设计

### 1. 数据加载策略

```typescript
// 数据加载优先级
1. 文章索引 (index.json) - 应用启动时加载
2. 当前文章数据 - 根据索引加载
3. 其他文章数据 - 按需加载和缓存
```

### 2. 状态管理结构

```typescript
interface AppState {
  // 文章相关
  articles: {
    index: ArticleIndex | null;
    current: Article | null;
    cache: Record<string, Article>;
    loading: boolean;
    error: string | null;
  };
  
  // 学习进度
  learning: {
    currentSentence: number;
    progress: LearningProgress;
    history: number[];
  };
  
  // UI 状态
  ui: {
    showArticleSelector: boolean;
    showLevelModal: boolean;
    selectedLevel: Level | null;
  };
}
```

### 3. 数据持久化策略

```typescript
// 需要持久化的数据
interface PersistedState {
  currentArticleId: string;
  articleProgress: Record<string, {
    currentSentence: number;
    completedSentences: number[];
    lastAccessTime: string;
  }>;
  userPreferences: {
    preferredLevels: Level[];
    autoSave: boolean;
  };
}
```

## 组件交互设计

### 1. AppHeader 组件交互

```typescript
interface AppHeaderProps {
  currentArticle: Article | null;
  onMenuPress: () => void;
  onLevelSelect: (level: Level) => void;
  availableLevels: Level[];
}

// 交互行为
- 菜单按钮: 触发文章选择器显示
- 文章标题: 显示当前文章信息
- 级别选择器: 下拉选择并显示完整文章
```

### 2. ArticleSelector 组件交互

```typescript
interface ArticleSelectorProps {
  visible: boolean;
  articles: ArticleMetadata[];
  currentArticleId: string;
  onSelect: (articleId: string) => void;
  onClose: () => void;
}

// 交互行为
- 文章列表: 显示所有可用文章
- 选择文章: 切换到新文章
- 关闭按钮: 取消选择
```

### 3. NavigationControls 组件交互

```typescript
interface NavigationControlsProps {
  currentIndex: number;
  totalCount: number;
  onPrevious: () => void;
  onNext: () => void;
  canGoBack: boolean;
  canGoForward: boolean;
}

// 交互行为
- 上一句按钮: 导航到前一句
- 下一句按钮: 导航到后一句
- 按钮状态: 根据边界条件禁用
```

## 错误处理策略

### 1. 网络错误处理

```typescript
// 错误类型定义
enum ErrorType {
  NETWORK_ERROR = 'network_error',
  DATA_PARSE_ERROR = 'data_parse_error',
  FILE_NOT_FOUND = 'file_not_found',
  INVALID_DATA = 'invalid_data'
}

// 错误处理流程
1. 捕获错误
2. 分类错误类型
3. 显示用户友好的错误信息
4. 提供重试机制
5. 记录错误日志
```

### 2. 数据验证

```typescript
// 数据验证规则
const validateArticle = (article: any): article is Article => {
  return (
    typeof article.id === 'string' &&
    typeof article.title === 'string' &&
    article.jlptData &&
    validateJLPTData(article.jlptData)
  );
};

const validateJLPTData = (data: any): data is JLPTData => {
  return (
    data.N5 && Array.isArray(data.N5.sentences) &&
    data.N4 && Array.isArray(data.N4.sentences) &&
    // ... 其他级别验证
  );
};
```

## 性能优化策略

### 1. 数据加载优化

```typescript
// 懒加载策略
- 文章索引: 应用启动时加载
- 当前文章: 立即加载
- 其他文章: 用户选择时加载
- 缓存策略: LRU 缓存最近访问的 3 篇文章
```

### 2. 渲染优化

```typescript
// React 优化技术
- useMemo: 缓存计算结果
- useCallback: 缓存事件处理函数
- React.memo: 防止不必要的重渲染
- 虚拟化: 如果文章数量很大时使用
```

### 3. 状态更新优化

```typescript
// 批量更新策略
- 使用 React 18 的自动批处理
- 避免频繁的状态更新
- 使用 useReducer 管理复杂状态
```

## 用户体验设计

### 1. 加载状态

```typescript
// 加载状态设计
interface LoadingState {
  articles: boolean;      // 文章列表加载中
  currentArticle: boolean; // 当前文章加载中
  switching: boolean;     // 文章切换中
}

// 加载指示器
- 骨架屏: 文章内容加载时
- 进度条: 数据加载进度
- 禁用状态: 操作进行中时禁用按钮
```

### 2. 过渡动画

```typescript
// 动画设计
- 文章切换: 淡入淡出效果
- 句子导航: 滑动效果
- 模态框: 缩放和淡入效果
- 按钮交互: 点击反馈动画
```

### 3. 反馈机制

```typescript
// 用户反馈
- 成功提示: 文章切换成功
- 错误提示: 加载失败时的友好提示
- 进度指示: 当前学习进度
- 状态指示: 按钮可用性状态
```

## 测试策略

### 1. 单元测试

```typescript
// 测试覆盖范围
- 数据加载函数
- 状态管理 hooks
- 数据验证函数
- 工具函数
```

### 2. 集成测试

```typescript
// 测试场景
- 文章切换流程
- 句子导航流程
- 错误处理流程
- 数据持久化
```

### 3. 用户测试

```typescript
// 测试用例
- 新用户首次使用
- 文章切换操作
- 网络异常情况
- 不同设备尺寸
```

## 部署和监控

### 1. 部署策略

```typescript
// 渐进式部署
1. 内部测试版本
2. Beta 版本发布
3. 正式版本发布
4. 监控和反馈收集
```

### 2. 监控指标

```typescript
// 关键指标
- 应用启动时间
- 文章加载时间
- 错误率
- 用户留存率
- 功能使用率
```

这个功能流程设计为应用的重构提供了详细的技术指导和实现策略。
