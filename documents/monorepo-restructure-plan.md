# Monorepo 架构调整方案

## 概述

将 Dokkai 项目重构为简化的 monorepo 架构，分离 mobile 和 service 模块，保持最小化共享以降低开发成本。

## 目标架构

### 目录结构
```
dokkai/
├── apps/
│   ├── mobile/              # React Native App (原有代码迁移)
│   │   ├── src/
│   │   ├── package.json     # 独立依赖管理
│   │   ├── app.config.js
│   │   ├── eas.json
│   │   └── tsconfig.json
│   │
│   └── service/             # Cloudflare Worker API
│       ├── src/
│       │   ├── index.ts     # Worker 入口
│       │   ├── handlers/    # API 处理器
│       │   ├── types/       # 服务端类型
│       │   └── utils/       # 工具函数
│       ├── package.json     # 独立依赖管理
│       ├── wrangler.toml    # Cloudflare 配置
│       └── tsconfig.json
│
├── shared/                  # 最小化共享
│   ├── types.ts            # API 契约类型定义
│   └── constants.ts        # 共享常量
│
├── data/                   # 共享数据文件 (保持不变)
├── docs/                   # 文档 (保持不变)
├── scripts/                # 开发和部署脚本
│   ├── dev.sh             # 本地开发脚本
│   └── deploy.sh          # 部署脚本
├── package.json            # 根配置 (最小化)
└── README.md              # 更新的使用说明
```

## 设计原则

### 1. 最小化共享
- **只共享**: API 契约类型、常量
- **不共享**: 业务逻辑、工具函数、配置文件、依赖包

### 2. 模块独立性
- 每个 app 有独立的 package.json
- 每个 app 有独立的构建和部署流程
- 互不影响的开发环境

### 3. 简化管理
- 不使用 workspace 机制
- 通过简单的文件引用共享类型
- 手动管理但成本可控

## 共享内容定义

### shared/types.ts
```typescript
// 只包含 API 契约相关的类型
export interface Article {
  id: string;
  title: string;
  description: string;
  author: string;
  createdAt: string;
  tags: string[];
  category: string;
  difficulty: string;
  estimatedReadingTime: number;
  sentenceCount: number;
  jlptData?: JLPTData;
}

export interface JLPTData {
  [key in Level]: LevelData;
}

export type Level = 'N5' | 'N4' | 'N3' | 'N2' | 'N1';

export interface LevelData {
  sentences: string[];
  grammar_features: string[];
  full_text: string;
  grammar_points: string[];
  word_count: number;
  description: string;
  audio_metadata?: AudioMetadata[];
}

export interface AudioMetadata {
  sentence_index: number;
  audio_path: string;
  duration: number;
  file_size: number;
  word_timings: WordTiming[];
}

export interface WordTiming {
  word: string;
  start_time: number;
  end_time: number;
  reading?: string;
  part_of_speech?: string;
  definition?: string;
}

// API 响应类型
export interface ArticleListResponse {
  articles: Article[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

export interface AudioResponse {
  audio_url: string;
  timing_url?: string;
  duration: number;
  file_size: number;
  expires_at: string;
}
```

### shared/constants.ts
```typescript
// 只包含需要同步的常量
export const API_ENDPOINTS = {
  ARTICLES: '/api/articles',
  AUDIO: '/api/audio',
  HEALTH: '/api/health',
} as const;

export const JLPT_LEVELS = ['N5', 'N4', 'N3', 'N2', 'N1'] as const;

export const CATEGORIES = [
  'daily-life',
  'school-life', 
  'travel',
  'social-life',
  'culture',
  'work-life'
] as const;

export const DIFFICULTIES = [
  'beginner',
  'intermediate', 
  'advanced'
] as const;

export const DEFAULT_PAGINATION = {
  page: 1,
  limit: 20,
  maxLimit: 100
} as const;
```

## 迁移步骤

### Phase 1: 目录结构调整

1. **创建新目录结构**
   ```bash
   mkdir -p apps/mobile apps/service shared scripts
   ```

2. **迁移 mobile 代码**
   ```bash
   # 移动现有代码到 apps/mobile
   mv src apps/mobile/
   mv components apps/mobile/src/
   mv hooks apps/mobile/src/
   mv services apps/mobile/src/
   mv styles apps/mobile/src/
   mv types apps/mobile/src/
   mv utils apps/mobile/src/
   
   # 移动配置文件
   mv app.config.js apps/mobile/
   mv eas.json apps/mobile/
   mv tsconfig.json apps/mobile/
   ```

3. **创建独立的 package.json**
   ```bash
   # 复制依赖到 mobile
   cp package.json apps/mobile/package.json
   # 手动编辑，只保留 mobile 相关依赖
   ```

4. **创建共享文件**
   ```bash
   touch shared/types.ts shared/constants.ts
   # 从现有代码中提取类型定义
   ```

### Phase 2: Service 初始化

1. **创建基础结构**
   ```bash
   cd apps/service
   npm init -y
   npm install @cloudflare/workers-types
   npm install -D typescript wrangler
   ```

2. **初始化 Wrangler 配置**
   ```bash
   wrangler init
   # 生成 wrangler.toml
   ```

3. **创建最小 Worker 实现**
   - 迁移 api-server 的核心功能
   - 实现文章列表和详情 API
   - 添加健康检查端点

### Phase 3: 配置调整

1. **更新 Mobile 应用配置**
   - 调整 tsconfig.json 路径
   - 更新 import 路径使用共享类型
   - 配置 API 端点

2. **创建开发脚本**
   - `scripts/dev.sh` - 本地开发
   - `scripts/deploy.sh` - 部署脚本

3. **更新根配置**
   - 简化 package.json
   - 更新 README.md

## 开发脚本设计

### scripts/dev.sh
```bash
#!/bin/bash

echo "🚀 Dokkai Development Environment"
echo ""
echo "选择要启动的服务:"
echo "1) Mobile App only"
echo "2) Service only" 
echo "3) Both (recommended)"
echo ""

read -p "请选择 (1-3): " choice

case $choice in
  1)
    echo "📱 Starting Mobile App..."
    cd apps/mobile && npm start
    ;;
  2)
    echo "🔧 Starting Service..."
    cd apps/service && npm run dev
    ;;
  3)
    echo "🚀 Starting both services..."
    echo ""
    echo "📱 Mobile App will be available at: http://localhost:8081"
    echo "🔧 Service will be available at: http://localhost:8787"
    echo ""
    echo "Press Ctrl+C to stop all services"
    
    # 启动 Service
    cd apps/service && npm run dev &
    SERVICE_PID=$!
    
    # 等待 Service 启动
    sleep 3
    
    # 启动 Mobile App
    cd ../mobile && npm start &
    MOBILE_PID=$!
    
    # 清理函数
    cleanup() {
      echo ""
      echo "🛑 Stopping all services..."
      kill $SERVICE_PID $MOBILE_PID 2>/dev/null
      exit 0
    }
    
    trap cleanup SIGINT SIGTERM
    wait
    ;;
  *)
    echo "❌ Invalid choice"
    exit 1
    ;;
esac
```

### scripts/deploy.sh
```bash
#!/bin/bash

echo "🚀 Dokkai Deployment Script"
echo ""
echo "选择部署目标:"
echo "1) Mobile App (EAS Build)"
echo "2) Service (Cloudflare Worker)"
echo "3) Both"
echo ""

read -p "请选择 (1-3): " choice

deploy_mobile() {
  echo "📱 Deploying Mobile App..."
  cd apps/mobile
  
  echo "选择构建类型:"
  echo "1) Development Build"
  echo "2) Preview Build" 
  echo "3) Production Build"
  
  read -p "请选择 (1-3): " build_type
  
  case $build_type in
    1) eas build --profile development --platform all ;;
    2) eas build --profile preview --platform all ;;
    3) eas build --profile production --platform all ;;
    *) echo "❌ Invalid choice"; exit 1 ;;
  esac
}

deploy_service() {
  echo "🔧 Deploying Service..."
  cd apps/service
  
  echo "选择部署环境:"
  echo "1) Development"
  echo "2) Staging"
  echo "3) Production"
  
  read -p "请选择 (1-3): " env_type
  
  case $env_type in
    1) wrangler deploy --env development ;;
    2) wrangler deploy --env staging ;;
    3) wrangler deploy --env production ;;
    *) echo "❌ Invalid choice"; exit 1 ;;
  esac
}

case $choice in
  1) deploy_mobile ;;
  2) deploy_service ;;
  3) 
    deploy_service
    echo ""
    deploy_mobile
    ;;
  *) echo "❌ Invalid choice"; exit 1 ;;
esac

echo "✅ Deployment completed!"
```

## Service 最小实现计划

### 1. 基础 Worker 结构
```typescript
// apps/service/src/index.ts
export interface Env {
    // 环境变量类型定义
}

export default {
    async fetch(request: Request, env: Env): Promise<Response> {
        // 基础路由处理
    }
};
```

### 2. 迁移 API Server 功能
- **文章相关 API**: 从 `api-server/src/routes/articles.ts` 迁移
- **数据服务**: 从 `api-server/src/services/dataService.ts` 迁移
- **工具函数**: 从 `api-server/src/utils/` 迁移

### 3. 数据存储策略
- **初期**: 直接读取 data/ 目录的 JSON 文件
- **后期**: 迁移到 D1 + KV + R2

### 4. Mock 音频功能
- 返回占位符音频 URL
- 提供基础的音频元数据
- 为后续真实音频功能做准备

## 配置变更

### 根 package.json (简化版)
```json
{
  "name": "dokkai",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "./scripts/dev.sh",
    "deploy": "./scripts/deploy.sh",
    "dev:mobile": "cd apps/mobile && npm start",
    "dev:service": "cd apps/service && npm run dev",
    "build:mobile": "cd apps/mobile && npm run build",
    "build:service": "cd apps/service && npm run build",
    "type-check": "cd apps/mobile && npm run type-check && cd ../service && npm run type-check"
  },
  "devDependencies": {
    "typescript": "^5.0.0"
  }
}
```

### README.md 更新要点
1. **项目架构说明**
2. **开发环境设置**
3. **本地开发流程**
4. **部署步骤说明**
5. **故障排除指南**

## 验证标准

重构完成后需要验证：

1. **Mobile App**:
   - ✅ 能正常启动和运行
   - ✅ 类型导入正确
   - ✅ 能连接到本地 Service

2. **Service**: 
   - ✅ 能正常启动 (`wrangler dev`)
   - ✅ API 端点正常响应
   - ✅ 能读取 data 目录数据

3. **集成**:
   - ✅ Mobile 能调用 Service API
   - ✅ 数据格式兼容
   - ✅ 错误处理正常

4. **开发体验**:
   - ✅ `./scripts/dev.sh` 能正常启动
   - ✅ 类型修改能及时同步
   - ✅ 热重载正常工作

## 风险评估

### 潜在问题
1. **Import 路径调整**: 大量文件需要更新导入路径
2. **类型定义迁移**: 可能有遗漏或不兼容
3. **API 兼容性**: Service 实现可能与 Mobile 期望不匹配

### 缓解措施
1. **分步迁移**: 先确保基础功能正常，再逐步完善
2. **保留备份**: 迁移前创建 git 分支备份
3. **增量验证**: 每个步骤都验证功能正常

## 后续优化方向

1. **Service 功能完善**: 实现完整的 API 功能
2. **数据库集成**: 从文件系统迁移到 Cloudflare D1
3. **音频功能**: 集成真实的音频生成和存储
4. **性能优化**: 添加缓存和 CDN 策略
5. **监控告警**: 添加日志和错误监控

## 总结

这个重构方案保持了 monorepo 的优势（类型共享、统一管理），同时最小化了模块间耦合。通过独立的依赖管理和简单的文件引用机制，既能享受类型安全的好处，又能保持各模块的独立性，开发成本可控。