# Cloudflare Worker 架构设计

## 概述

本文档描述了 Dokkai 日语学习应用基于 Cloudflare Worker 的后端架构设计，包括数据存储策略、API 设计和性能优化方案。

## 架构目标

- 支持大量文章内容和音频文件
- 全球低延迟访问
- 成本效益最优
- 易于维护和扩展
- 支持离线使用

## 整体架构

```
Cloudflare Worker API
├── D1 Database (SQLite)
│   ├── articles (文章元数据)
│   ├── audio_metadata (音频文件元信息)
│   └── user_progress (用户学习进度，可选)
│
├── KV Store (键值存储)
│   └── article:{id} → JLPTData (完整的文章内容)
│
└── R2 Storage (对象存储)
    └── audio/{article_id}/{level}/{sentence_index}.mp3
```

## 数据存储策略

### 1. D1 Database - 结构化数据

**articles 表:**
```sql
CREATE TABLE articles (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  category TEXT NOT NULL,
  tags TEXT NOT NULL, -- JSON array
  difficulty TEXT NOT NULL,
  estimated_reading_time INTEGER,
  sentence_count INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**audio_metadata 表:**
```sql
CREATE TABLE audio_metadata (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id TEXT NOT NULL,
  level TEXT NOT NULL, -- N5, N4, N3, N2, N1
  sentence_index INTEGER NOT NULL,
  file_size INTEGER,
  duration INTEGER, -- 音频时长(秒)
  file_path TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles(id),
  UNIQUE(article_id, level, sentence_index)
);
```

**user_progress 表 (可选，支持用户系统时):**
```sql
CREATE TABLE user_progress (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT NOT NULL,
  article_id TEXT NOT NULL,
  current_sentence INTEGER DEFAULT 0,
  completed_sentences TEXT, -- JSON array of completed sentence indices
  last_accessed DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles(id),
  UNIQUE(user_id, article_id)
);
```

### 2. KV Store - 文章内容

**存储结构:**
- Key: `article:{article_id}`
- Value: 完整的 JLPTData 对象

**选择 KV 的原因:**
- 读取速度快 (5-20ms)
- 全球边缘缓存
- 适合频繁访问的文本内容
- 25MB 大小限制对文本内容足够

### 3. R2 Storage - 音频文件

**文件路径结构:**
```
audio/
├── {article_id}/
│   ├── N5/
│   │   ├── 0.mp3
│   │   ├── 1.mp3
│   │   └── ...
│   ├── N4/
│   ├── N3/
│   ├── N2/
│   └── N1/
```

**选择 R2 的原因:**
- 无大小限制
- CDN 加速访问
- 成本效益高
- 支持预签名 URL

## API 设计

### 核心端点

#### 1. 获取文章列表
```typescript
GET /api/articles?page=1&limit=20&category=daily-life&difficulty=beginner

Response:
{
  "articles": [
    {
      "id": "daily-life-student",
      "title": "大学生の日常生活",
      "category": "daily-life",
      "tags": ["日常生活", "大学生", "基礎会話"],
      "difficulty": "beginner",
      "estimatedReadingTime": 5,
      "sentenceCount": 10
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "hasMore": true
  }
}
```

#### 2. 获取文章详情
```typescript
GET /api/articles/{id}

Response:
{
  "id": "daily-life-student",
  "title": "大学生の日常生活",
  "category": "daily-life",
  "tags": ["日常生活", "大学生", "基礎会話"],
  "difficulty": "beginner",
  "jlptData": {
    "N5": {
      "sentences": ["..."],
      "grammar_features": ["..."],
      "full_text": "...",
      "grammar_points": ["..."],
      "word_count": 150,
      "description": "..."
    },
    // ... other levels
  }
}
```

#### 3. 获取音频文件
```typescript
GET /api/articles/{id}/audio/{level}/{sentence_index}

Response:
{
  "audioUrl": "https://r2-domain.com/audio/daily-life-student/N5/0.mp3",
  "duration": 3.5,
  "expiresAt": "2024-01-01T12:00:00Z"
}
```

#### 4. 批量获取音频 URL
```typescript
POST /api/articles/{id}/audio/batch
Body: {
  "requests": [
    {"level": "N5", "sentence_index": 0},
    {"level": "N5", "sentence_index": 1},
    {"level": "N5", "sentence_index": 2}
  ]
}

Response:
{
  "audioUrls": [
    {
      "level": "N5",
      "sentence_index": 0,
      "audioUrl": "https://...",
      "duration": 3.5
    }
    // ...
  ]
}
```

## 性能优化策略

### 1. 缓存策略

**Worker 层面:**
```typescript
// 热数据缓存在 Worker 内存中
const cache = new Map();

export default {
  async fetch(request, env) {
    const cacheKey = new URL(request.url).pathname;
    
    // 检查内存缓存
    if (cache.has(cacheKey)) {
      return new Response(cache.get(cacheKey));
    }
    
    // 查询数据并缓存
    const data = await queryDatabase(env.DB);
    cache.set(cacheKey, data);
    
    return new Response(data);
  }
};
```

**HTTP 缓存头:**
```typescript
// 文章列表 - 缓存1小时
response.headers.set('Cache-Control', 'public, max-age=3600');

// 文章内容 - 缓存24小时
response.headers.set('Cache-Control', 'public, max-age=86400');

// 音频文件 - 缓存7天
response.headers.set('Cache-Control', 'public, max-age=604800');
```

### 2. 数据库优化

**索引策略:**
```sql
-- 加速文章查询
CREATE INDEX idx_articles_category ON articles(category);
CREATE INDEX idx_articles_difficulty ON articles(difficulty);
CREATE INDEX idx_articles_created_at ON articles(created_at);

-- 加速音频查询
CREATE INDEX idx_audio_article_level ON audio_metadata(article_id, level);
```

**查询优化:**
```sql
-- 使用 LIMIT 避免全表扫描
SELECT * FROM articles WHERE category = ? LIMIT 20;

-- 预计算常用统计数据
CREATE VIEW article_stats AS
SELECT 
  category,
  COUNT(*) as article_count,
  AVG(sentence_count) as avg_sentences
FROM articles 
GROUP BY category;
```

### 3. 音频处理优化

**按需生成策略:**
```typescript
async function getAudioUrl(articleId: string, level: string, sentenceIndex: number) {
  // 1. 检查 R2 中是否已存在
  const audioPath = `audio/${articleId}/${level}/${sentenceIndex}.mp3`;
  const audioExists = await env.R2.head(audioPath);
  
  if (audioExists) {
    // 返回预签名 URL
    return await env.R2.sign(audioPath, { expiresIn: 3600 });
  }
  
  // 2. 不存在则生成音频
  const audioBuffer = await generateTTS(sentence);
  await env.R2.put(audioPath, audioBuffer);
  
  return await env.R2.sign(audioPath, { expiresIn: 3600 });
}
```

## 延迟分析和优化

### 预期延迟表现

| 操作 | 冷启动 | 热启动 | 优化后 |
|------|--------|--------|---------|
| 文章列表 | 200-400ms | 50-100ms | 20-50ms |
| 文章详情 | 300-500ms | 100-200ms | 50-100ms |
| 音频 URL | 100-300ms | 50-100ms | 20-50ms |

### 冷启动优化

**定时预热:**
```typescript
// 使用 Cron Triggers 保持热启动
export default {
  async scheduled(event, env, ctx) {
    // 每5分钟预热一次
    await fetch('https://your-worker.dev/api/health');
  }
};
```

**智能预加载:**
```typescript
// 预加载热门文章到 KV
async function preloadPopularArticles() {
  const popular = await env.DB.prepare(
    'SELECT id FROM articles ORDER BY view_count DESC LIMIT 10'
  ).all();
  
  for (const article of popular.results) {
    await env.KV.get(`article:${article.id}`);
  }
}
```

## 成本估算

### Cloudflare 免费额度
- **Worker**: 100,000 requests/day
- **D1**: 25,000 rows read/day, 50,000 rows written/day
- **KV**: 100,000 reads/day, 1,000 writes/day
- **R2**: 1,000,000 Class A operations/month

### 预估使用量 (1000 活跃用户)
- **API 请求**: ~50,000/day (在免费额度内)
- **D1 读取**: ~30,000/day (在免费额度内)
- **KV 读取**: ~80,000/day (在免费额度内)
- **R2 存储**: ~100GB (~$2.5/month)

## 实施路线图

### Phase 1: 基础 API (Week 1-2)
- [ ] 设置 CF Worker 和 D1 数据库
- [ ] 实现文章列表和详情 API
- [ ] 数据迁移脚本

### Phase 2: KV 集成 (Week 3)
- [ ] 文章内容存储到 KV
- [ ] 缓存策略实现
- [ ] 性能测试和优化

### Phase 3: 音频支持 (Week 4-5)
- [ ] R2 存储集成
- [ ] 音频元数据管理
- [ ] TTS 服务集成 (可选)

### Phase 4: 高级功能 (Week 6+)
- [ ] 用户进度跟踪
- [ ] 分析和监控
- [ ] 缓存预热机制

## 监控和维护

### 关键指标
- API 响应时间
- 错误率
- 缓存命中率
- 存储使用量

### 告警设置
- 响应时间 > 1s
- 错误率 > 1%
- 存储使用超过预算

### 日志和调试
```typescript
console.log('API Request:', {
  path: request.url,
  method: request.method,
  timestamp: Date.now(),
  userAgent: request.headers.get('User-Agent')
});
```

## 总结

这个基于 Cloudflare Worker 的架构设计能够：

1. **处理大规模内容**: 支持数千篇文章和音频文件
2. **提供低延迟访问**: 全球边缘网络分发
3. **控制成本**: 充分利用免费额度，超出部分成本可控
4. **易于维护**: 无服务器架构，减少运维负担
5. **支持扩展**: 模块化设计，便于添加新功能

初期用户规模下，延迟主要体现在冷启动场景，但通过合理的缓存策略和客户端优化，可以提供良好的用户体验。