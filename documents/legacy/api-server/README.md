# Dokkai Mock API Server

这是 Dokkai 日语学习应用的 Mock API 服务器，用于模拟从 Cloudflare D1 和 R2 读取数据的行为。实际上 API 内部直接读取现有的 JSON 文件。

## 功能特性

- 🚀 完整的 RESTful API 接口
- 📊 支持文章列表、详情、搜索功能
- 🏷️ 支持分类和标签查询
- 📈 提供统计信息接口
- ⚡ 内存缓存提升性能
- 🔍 支持多种筛选和分页
- 📝 完整的错误处理和日志记录
- 🌐 CORS 支持，便于前端调试

## 安装和运行

### 1. 安装依赖

```bash
cd api-server
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

服务器将在 `http://localhost:3001` 启动。

### 3. 构建和生产运行

```bash
npm run build
npm start
```

## API 接口

### 基础信息

- **Base URL**: `http://localhost:3001/api`
- **Content-Type**: `application/json`

### 主要端点

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/articles` | 获取文章列表 |
| GET | `/api/articles/:id` | 获取文章详情 |
| GET | `/api/articles/:id/levels/:level` | 获取特定JLPT级别数据 |
| GET | `/api/search?q=关键词` | 搜索文章 |
| GET | `/api/categories` | 获取分类列表 |
| GET | `/api/tags` | 获取标签列表 |
| GET | `/api/stats` | 获取统计信息 |

### 示例请求

```bash
# 获取所有文章
curl http://localhost:3001/api/articles

# 获取特定文章
curl http://localhost:3001/api/articles/daily-life-student

# 搜索文章
curl "http://localhost:3001/api/search?q=大学生"

# 获取N5级别数据
curl http://localhost:3001/api/articles/daily-life-student/levels/N5

# 筛选文章
curl "http://localhost:3001/api/articles?category=daily-life&difficulty=beginner"
```

## 项目结构

```
api-server/
├── src/
│   ├── index.ts              # 服务器入口
│   ├── routes/               # 路由处理
│   │   ├── articles.ts
│   │   ├── search.ts
│   │   └── metadata.ts
│   ├── services/             # 服务层
│   │   ├── dataService.ts    # 数据读取服务
│   │   └── cacheService.ts   # 缓存服务
│   ├── types/                # 类型定义
│   │   └── api.ts
│   └── utils/                # 工具函数
│       ├── response.ts
│       ├── errors.ts
│       └── validation.ts
├── package.json
├── tsconfig.json
└── README.md
```

## 缓存机制

- **默认缓存时间**: 5分钟
- **分类/标签缓存**: 30分钟
- **统计信息缓存**: 10分钟
- **自动清理**: 每分钟清理过期缓存

## 调试端点

- `GET /api/cache-stats` - 查看缓存统计
- `POST /api/cache/clear` - 清空所有缓存
- `GET /health` - 健康检查

## 错误处理

所有 API 错误都返回统一格式：

```json
{
  "success": false,
  "error": "错误信息",
  "code": "错误代码",
  "details": "详细信息"
}
```

## 日志记录

服务器会记录：
- 每个请求的时间戳、方法、路径和IP
- 响应状态码和处理时间
- 缓存命中/未命中情况
- 错误详细信息

## 配置选项

可通过环境变量配置：

- `PORT`: 服务器端口（默认 3001）
- `NODE_ENV`: 运行环境（development/production）

## 开发说明

### 数据源

API 从以下位置读取数据：
- `../data/index.json` - 文章索引（模拟 D1 数据库）
- `../data/articles/*.json` - 文章内容（模拟 R2 存储）

### 添加新接口

1. 在对应的路由文件中添加新端点
2. 更新类型定义 (`src/types/api.ts`)
3. 添加必要的验证和缓存逻辑
4. 更新文档

### 性能优化

- 使用内存缓存减少文件读取
- 合理设置缓存过期时间
- 支持分页避免大量数据传输
- GZIP 压缩减少传输大小

## 与前端集成

前端可以通过以下方式使用 API：

```typescript
// 配置 API 基础URL
const API_BASE_URL = 'http://localhost:3001/api';

// 获取文章列表
const response = await fetch(`${API_BASE_URL}/articles`);
const data = await response.json();

if (data.success) {
  console.log('Articles:', data.data.articles);
}
```

## 故障排除

### 常见问题

1. **端口被占用**: 修改 PORT 环境变量
2. **数据文件不存在**: 确保 `../data/` 目录存在且包含必要文件
3. **CORS 错误**: 检查前端域名是否在 CORS 配置中

### 调试步骤

1. 检查服务器启动日志
2. 访问 `/health` 端点确认服务正常
3. 查看请求日志定位问题
4. 使用 `/api/cache-stats` 检查缓存状态