#!/usr/bin/env node

/**
 * API 服务器测试脚本
 * 验证所有主要端点是否正常工作
 */

const API_BASE_URL = 'http://localhost:3001';

// 简单的 HTTP 客户端
async function fetchApi(endpoint) {
  const url = `${API_BASE_URL}${endpoint}`;
  console.log(`🔍 Testing: ${url}`);
  
  try {
    const response = await fetch(url);
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log(`✅ ${endpoint} - OK`);
      return data;
    } else {
      console.log(`❌ ${endpoint} - Failed: ${data.error || 'Unknown error'}`);
      return null;
    }
  } catch (error) {
    console.log(`💥 ${endpoint} - Error: ${error.message}`);
    return null;
  }
}

async function testAllEndpoints() {
  console.log('🚀 Starting API tests...\n');

  // 1. 健康检查
  console.log('1. Health Check');
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    const data = await response.json();
    if (data.status === 'healthy') {
      console.log('✅ /health - Server is healthy');
    } else {
      console.log('❌ /health - Server is not healthy');
    }
  } catch (error) {
    console.log('💥 /health - Cannot connect to server');
    console.log('❓ Please make sure the API server is running with: npm run dev');
    return;
  }

  console.log('\n2. Article Endpoints');
  
  // 2. 获取文章列表
  const articlesResult = await fetchApi('/api/articles');
  let testArticleId = null;
  
  if (articlesResult && articlesResult.data && articlesResult.data.articles.length > 0) {
    testArticleId = articlesResult.data.articles[0].id;
    console.log(`   📄 Found ${articlesResult.data.articles.length} articles`);
    console.log(`   🔹 Test article ID: ${testArticleId}`);
  }

  // 3. 获取特定文章
  if (testArticleId) {
    await fetchApi(`/api/articles/${testArticleId}`);
  }

  // 4. 获取特定级别数据
  if (testArticleId) {
    await fetchApi(`/api/articles/${testArticleId}/levels/N5`);
  }

  console.log('\n3. Search and Metadata');
  
  // 5. 搜索功能
  await fetchApi('/api/search?q=大学生');
  
  // 6. 获取分类
  await fetchApi('/api/categories');
  
  // 7. 获取标签
  await fetchApi('/api/tags');
  
  // 8. 获取统计信息
  await fetchApi('/api/stats');

  console.log('\n4. Filter Tests');
  
  // 9. 筛选测试
  await fetchApi('/api/articles?category=daily-life');
  await fetchApi('/api/articles?difficulty=beginner');
  await fetchApi('/api/articles?tags=日常生活');

  console.log('\n5. Debug Endpoints');
  
  // 10. 缓存统计
  await fetchApi('/api/cache-stats');

  console.log('\n✨ API tests completed!\n');
  
  console.log('💡 Next steps:');
  console.log('   1. Start the Dokkai app: expo start');
  console.log('   2. Switch to API mode in the app');
  console.log('   3. Test the functionality');
  console.log('   4. Check API server logs for any errors');
}

// 运行测试
testAllEndpoints().catch(console.error);