{"name": "dokkai-api-server", "version": "1.0.0", "description": "Mock API server for Dokkai Japanese learning app", "main": "dist/index.js", "scripts": {"dev": "ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "clean": "rm -rf dist"}, "dependencies": {"express": "^4.18.0", "cors": "^2.8.5", "compression": "^1.7.4"}, "devDependencies": {"@types/express": "^4.17.0", "@types/cors": "^2.8.0", "@types/compression": "^1.7.0", "@types/node": "^20.0.0", "typescript": "^5.0.0", "ts-node": "^10.9.0"}, "keywords": ["dokkai", "api", "mock", "japanese", "learning"], "author": "Dokkai Team", "license": "MIT"}