import { Request, Response, NextFunction } from 'express';
import { createErrorResponse } from './response';

export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  console.error('API Error:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString(),
  });

  if (error instanceof ApiError) {
    res.status(error.statusCode).json(
      createErrorResponse(error.message, error.code, error.details)
    );
  } else {
    res.status(500).json(
      createErrorResponse('Internal server error', 'INTERNAL_ERROR')
    );
  }
}