export type Level = 'N5' | 'N4' | 'N3' | 'N2' | 'N1';

export interface LevelData {
  sentences: string[];
  grammar_features: string[];
  full_text: string;
  grammar_points: string[];
  word_count: number;
  color: string;
  bgColor: string;
  description: string;
}

export interface JLPTData {
  [key: string]: LevelData;
}

export interface ArticleMetadata {
  id: string;
  title: string;
  description: string;
  author?: string;
  createdAt: string;
  updatedAt?: string;
  tags: string[];
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadingTime: number;
  sentenceCount: number;
}

export interface Article {
  id: string;
  title: string;
  description: string;
  author?: string;
  createdAt: string;
  updatedAt?: string;
  tags: string[];
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadingTime: number;
  jlptData: JLPTData;
}

export interface ArticleIndex {
  version: string;
  lastUpdated: string;
  currentArticle: string;
  articles: ArticleMetadata[];
}

// API Response interfaces
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  details?: any;
}

export interface ArticleListResponse {
  articles: ArticleMetadata[];
  total: number;
  version: string;
  lastUpdated: string;
  currentArticle: string;
}

export interface LevelDataResponse {
  articleId: string;
  level: Level;
  data: LevelData;
}

export interface SearchResponse {
  articles: ArticleMetadata[];
  total: number;
  query: string;
}

export interface CategoriesResponse {
  categories: string[];
}

export interface TagsResponse {
  tags: string[];
}

export interface StatsResponse {
  totalArticles: number;
  totalCategories: number;
  totalTags: number;
  difficultyDistribution: Record<string, number>;
  categoryDistribution: Record<string, number>;
  version: string;
  lastUpdated: string;
}