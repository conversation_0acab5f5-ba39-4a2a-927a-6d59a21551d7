import { Router } from 'express';
import { dataService } from '../services/dataService';
import { cacheService } from '../services/cacheService';
import { createSuccessResponse } from '../utils/response';

const router = Router();

// GET /api/categories - 获取分类列表
router.get('/categories', async (req, res, next) => {
  try {
    // 尝试从缓存获取
    const cacheKey = 'categories:list';
    const cached = cacheService.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for categories: ${cacheKey}`);
      return res.json(cached);
    }

    const categories = await dataService.getCategories();
    const response = createSuccessResponse({ categories });

    // 缓存结果（较长时间，因为分类变化不频繁）
    cacheService.set(cacheKey, response, 30 * 60 * 1000); // 30分钟
    console.log(`Cached categories: ${cacheKey}`);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

// GET /api/tags - 获取标签列表
router.get('/tags', async (req, res, next) => {
  try {
    // 尝试从缓存获取
    const cacheKey = 'tags:list';
    const cached = cacheService.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for tags: ${cacheKey}`);
      return res.json(cached);
    }

    const tags = await dataService.getTags();
    const response = createSuccessResponse({ tags });

    // 缓存结果（较长时间，因为标签变化不频繁）
    cacheService.set(cacheKey, response, 30 * 60 * 1000); // 30分钟
    console.log(`Cached tags: ${cacheKey}`);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

// GET /api/stats - 获取统计信息
router.get('/stats', async (req, res, next) => {
  try {
    const cacheKey = 'stats:summary';
    const cached = cacheService.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for stats: ${cacheKey}`);
      return res.json(cached);
    }

    const index = await dataService.getArticleIndex();
    const categories = await dataService.getCategories();
    const tags = await dataService.getTags();

    // 统计各难度级别的文章数量
    const difficultyStats = index.articles.reduce((acc, article) => {
      acc[article.difficulty] = (acc[article.difficulty] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // 统计各分类的文章数量
    const categoryStats = index.articles.reduce((acc, article) => {
      acc[article.category] = (acc[article.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const response = createSuccessResponse({
      totalArticles: index.articles.length,
      totalCategories: categories.length,
      totalTags: tags.length,
      difficultyDistribution: difficultyStats,
      categoryDistribution: categoryStats,
      version: index.version,
      lastUpdated: index.lastUpdated,
    });

    // 缓存结果
    cacheService.set(cacheKey, response, 10 * 60 * 1000); // 10分钟
    console.log(`Cached stats: ${cacheKey}`);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

// GET /api/cache-stats - 获取缓存统计信息（调试用）
router.get('/cache-stats', (req, res) => {
  const cacheStats = cacheService.getStats();
  const dataStats = dataService.getCacheStats();
  
  res.json(createSuccessResponse({
    cache: cacheStats,
    data: dataStats,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
  }));
});

// POST /api/cache/clear - 清理缓存（调试用）
router.post('/cache/clear', (req, res) => {
  cacheService.clear();
  dataService.clearCache();
  
  res.json(createSuccessResponse({
    message: 'All caches cleared successfully',
    timestamp: new Date().toISOString(),
  }));
});

export { router as metadataRouter };