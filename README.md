# Dokkai - JLPT 日语学习应用

Dokkai 专注于通过不同 JLPT 级别的对比来帮助学习者理解日语表达的层次和复杂性。

## 功能特点

### 🎯 核心功能
- **多级别对比学习**: 同一内容的 N5-N1 级别表达对比
- **文章切换**: 支持多篇文章，涵盖不同主题和场景
- **进度跟踪**: 自动保存学习进度，支持断点续学
- **语法解析**: 每个级别都有对应的语法特点说明

### 📱 用户界面
- **简洁的头部导航**: 文章标题、菜单按钮、级别选择器
- **卡片式布局**: 清晰展示不同级别的表达方式
- **底部导航**: 上一句/下一句快速切换
- **模态框**: 完整文章查看和级别选择

### 📚 内容管理
- **文章系统**: 结构化的文章数据管理
- **标签分类**: 支持按主题、难度、类别筛选
- **元数据**: 阅读时间估算、句子数量等信息

## 技术架构

### Monorepo 结构
- **React Native**: 跨平台移动应用开发 (apps/mobile)
- **Cloudflare Workers**: API 服务 (apps/service)
- **Expo**: 开发工具链和部署平台

### 项目结构
```
dokkai/
├── apps/                   # 应用程序
│   ├── mobile/            # React Native 移动应用
│   │   ├── app/           # Expo Router 页面
│   │   ├── src/           # 源代码
│   │   │   ├── components/ # UI 组件
│   │   │   ├── hooks/     # 自定义 Hooks
│   │   │   ├── services/  # 业务逻辑服务
│   │   │   ├── types/     # TypeScript 类型
│   │   │   ├── styles/    # 样式和主题
│   │   │   └── utils/     # 工具函数
│   │   └── data/          # 本地数据文件
│   │       ├── index.json # 文章索引
│   │       └── articles/  # 文章数据
│   └── service/           # Cloudflare Worker API
│       ├── src/           # Worker 源代码
│       │   ├── handlers/  # API 处理器
│       │   └── index.ts   # Worker 入口
│       └── wrangler.toml  # Cloudflare 配置
├── shared/                # 共享类型和工具 (计划中)
├── scripts/               # 构建和部署脚本
├── api-server/            # Express.js 参考实现 (待迁移)
└── data/                  # 共享数据文件 (计划中)
```

## 快速开始

### 1. 安装依赖
```bash
# 安装根目录依赖
npm install

# 安装移动应用依赖
cd apps/mobile && npm install

# 安装服务依赖
cd ../service && npm install
```

### 2. 启动开发环境

#### 选项 A: 只启动移动应用
```bash
cd apps/mobile
npx expo start
```

#### 选项 B: 启动 API 服务器 (参考实现)
```bash
cd api-server
npm run dev
```

#### 选项 C: 启动 Cloudflare Worker (开发中)
```bash
cd apps/service
npm run dev
```

### 3. 运行移动应用
在 Expo 输出中选择合适的平台：
- **Android 模拟器**: 按 `a`
- **iOS 模拟器**: 按 `i`
- **Web 浏览器**: 按 `w`
- **Expo Go**: 扫描二维码

## 数据结构

### 文章格式
每篇文章包含完整的 JLPT 数据结构：

```typescript
interface Article {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  estimatedReadingTime: number;
  jlptData: {
    N5: LevelData;
    N4: LevelData;
    N3: LevelData;
    N2: LevelData;
    N1: LevelData;
  };
}
```

### 级别数据
每个级别包含：
- `sentences`: 句子数组
- `grammar_features`: 语法特点说明
- `full_text`: 完整文本
- `grammar_points`: 语法要点
- `word_count`: 字数统计

## 开发指南

### 环境配置

1. 移动应用环境配置：
   ```bash
   cd apps/mobile
   cp .env.example .env
   ```

2. 配置数据源模式：
   ```bash
   # 本地模式（默认）
   DATA_SOURCE_MODE=local
   API_BASE_URL=

   # API模式
   DATA_SOURCE_MODE=api
   API_BASE_URL=http://localhost:3001/api
   ```

3. **重要**：修改 `.env` 文件后需要重启 Expo 开发服务器才能生效

### 数据源模式

- **本地模式**: 默认模式，从 `apps/mobile/data/` 读取 JSON 文件
- **API模式**: 从 API 服务器读取数据，需要同时配置 `DATA_SOURCE_MODE=api` 和有效的 `API_BASE_URL`
- **调试信息**: 数据源配置和API调用信息只在console中显示

### 添加新文章
1. 在 `apps/mobile/data/articles/` 目录创建新的 JSON 文件
2. 更新 `apps/mobile/data/index.json` 添加文章元数据
3. 在 `apps/mobile/src/services/articleService.ts` 中添加文件加载逻辑

### 自定义主题
修改 `apps/mobile/src/styles/theme.ts` 文件来调整：
- 颜色方案
- 字体大小
- 间距设置
- 阴影效果

### 扩展功能
- 在 `apps/mobile/src/hooks/` 目录添加新的状态管理逻辑
- 在 `apps/mobile/src/components/` 目录创建新的 UI 组件
- 在 `apps/mobile/src/services/` 目录添加新的业务逻辑

### API 服务开发

#### 当前状态
- **api-server/**: 完整的 Express.js 参考实现，包含所有 API 功能
- **apps/service/**: Cloudflare Worker 框架，功能正在迁移中

#### 开发 Cloudflare Worker
1. 参考 `api-server/` 中的实现
2. 在 `apps/service/src/handlers/` 中实现对应功能
3. 使用 `npm run dev` 在本地测试
4. 使用 `npm run deploy:dev` 部署到开发环境

## 测试

运行测试套件：
```bash
npm test
```

手动测试数据服务：
```typescript
import { runManualTests } from './__tests__/articleService.test';
runManualTests();
```

## 架构变更说明

### Monorepo 迁移
项目已从单体应用重构为 monorepo 架构：

1. **apps/mobile**: React Native 移动应用
   - 保持所有原有功能
   - 改进的组件结构和类型安全
   - 支持本地和 API 数据源模式

2. **apps/service**: Cloudflare Worker API 服务
   - 正在从 `api-server/` 迁移功能
   - 支持边缘计算和全球分发
   - 计划支持 D1 数据库和 R2 存储

3. **api-server**: Express.js 参考实现
   - 完整的 API 功能实现
   - 作为 Cloudflare Worker 开发参考
   - 包含缓存、验证、错误处理等

### 主要改进
1. **去除 Tab 导航**: 改为 Stack 导航，界面更简洁
2. **新增 Header 组件**: 集成文章标题、菜单、级别选择
3. **文章管理系统**: 支持多文章切换和管理
4. **底部导航**: 上一句/下一句操作更便捷
5. **数据结构优化**: 更好的类型安全和错误处理
6. **Monorepo 架构**: 更好的代码组织和部署策略

### 新增组件
- `AppHeader`: 应用头部导航
- `ArticleSelector`: 文章选择模态框
- `useArticleData`: 文章数据管理 Hook
- `articleService`: 文章数据服务

这个重构保持了所有原有功能，同时提供了更好的用户体验、代码组织结构和部署灵活性。
