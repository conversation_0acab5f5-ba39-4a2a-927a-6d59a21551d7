#!/bin/bash

echo "🔄 Dokkai Monorepo Migration Script"
echo "===================================="
echo ""

# 检查是否在项目根目录
if [ ! -f "package.json" ] || [ ! -d "components" ]; then
    echo "❌ Error: This script must be run from the project root directory"
    echo "   Make sure you're in the dokkai directory with package.json and components/ folder"
    exit 1
fi

# 创建备份
echo "📦 Creating backup..."
git add .
git commit -m "backup: before monorepo migration" || echo "⚠️  No changes to commit"
git branch backup-before-migration 2>/dev/null || echo "ℹ️  Backup branch already exists"

# 创建新目录结构
echo "📁 Creating new directory structure..."
mkdir -p apps/mobile apps/service/src shared scripts

# 移动 mobile 相关文件
echo "📱 Migrating mobile app files..."

# 创建 mobile/src 目录
mkdir -p apps/mobile/src

# 移动组件和源代码目录
if [ -d "components" ]; then
    mv components apps/mobile/src/
    echo "   ✅ Moved components/ to apps/mobile/src/"
fi

if [ -d "hooks" ]; then
    mv hooks apps/mobile/src/
    echo "   ✅ Moved hooks/ to apps/mobile/src/"
fi

if [ -d "services" ]; then
    mv services apps/mobile/src/
    echo "   ✅ Moved services/ to apps/mobile/src/"
fi

if [ -d "styles" ]; then
    mv styles apps/mobile/src/
    echo "   ✅ Moved styles/ to apps/mobile/src/"
fi

if [ -d "types" ]; then
    mv types apps/mobile/src/
    echo "   ✅ Moved types/ to apps/mobile/src/"
fi

if [ -d "utils" ]; then
    mv utils apps/mobile/src/
    echo "   ✅ Moved utils/ to apps/mobile/src/"
fi

# 移动 app 目录（Expo Router 结构）
if [ -d "app" ]; then
    mv app apps/mobile/
    echo "   ✅ Moved app/ to apps/mobile/"
fi

# 移动配置文件
if [ -f "app.config.js" ]; then
    mv app.config.js apps/mobile/
    echo "   ✅ Moved app.config.js to apps/mobile/"
fi

if [ -f "eas.json" ]; then
    mv eas.json apps/mobile/
    echo "   ✅ Moved eas.json to apps/mobile/"
fi

if [ -f "tsconfig.json" ]; then
    cp tsconfig.json apps/mobile/tsconfig.json
    echo "   ✅ Copied tsconfig.json to apps/mobile/"
fi

# 复制 package.json 到 mobile（稍后手动编辑）
cp package.json apps/mobile/package.json
echo "   ✅ Copied package.json to apps/mobile/ (needs manual editing)"

# 移动或清理 api-server 相关文件
echo "🔧 Handling api-server files..."
if [ -d "api-server" ]; then
    echo "   📋 Found api-server directory, will use it as reference for service implementation"
    # 不删除，保留作为参考
fi

# 创建基础的 service 文件
echo "⚙️  Creating basic service structure..."

# 创建 service package.json
cat > apps/service/package.json << 'EOF'
{
  "name": "dokkai-service",
  "version": "1.0.0",
  "description": "Dokkai API Service - Cloudflare Worker",
  "main": "src/index.ts",
  "scripts": {
    "dev": "wrangler dev --local --persist-to ./local-data",
    "dev:remote": "wrangler dev",
    "build": "tsc && wrangler deploy --dry-run",
    "deploy:dev": "wrangler deploy --env development",
    "deploy:staging": "wrangler deploy --env staging", 
    "deploy:prod": "wrangler deploy --env production",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {},
  "devDependencies": {
    "@cloudflare/workers-types": "^4.0.0",
    "typescript": "^5.0.0",
    "wrangler": "^3.0.0"
  }
}
EOF
echo "   ✅ Created apps/service/package.json"

# 创建 service tsconfig.json
cat > apps/service/tsconfig.json << 'EOF'
{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "target": "ES2022",
    "module": "ES2022",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "types": ["@cloudflare/workers-types"],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@shared/*": ["../../shared/*"]
    }
  },
  "include": ["src/**/*", "../../shared/**/*"],
  "exclude": ["node_modules", "dist"]
}
EOF
echo "   ✅ Created apps/service/tsconfig.json"

# 创建基础的 wrangler.toml
cat > apps/service/wrangler.toml << 'EOF'
name = "dokkai-service"
main = "src/index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[env.development]
name = "dokkai-service-dev"
vars = { ENVIRONMENT = "development" }

[env.staging]
name = "dokkai-service-staging" 
vars = { ENVIRONMENT = "staging" }

[env.production]
name = "dokkai-service-production"
vars = { ENVIRONMENT = "production" }
EOF
echo "   ✅ Created apps/service/wrangler.toml"

# 创建基础的 Worker 入口文件
mkdir -p apps/service/src/handlers
cat > apps/service/src/index.ts << 'EOF'
import { handleArticles } from './handlers/articles';

export interface Env {
  ENVIRONMENT: string;
}

export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    // CORS headers for development
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // Handle CORS preflight
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: corsHeaders,
      });
    }

    const url = new URL(request.url);
    console.log(`${request.method} ${url.pathname}`);

    try {
      let response: Response;

      // Route handling
      if (url.pathname.startsWith('/api/articles')) {
        response = await handleArticles(request, env);
      } else if (url.pathname === '/api/health') {
        response = new Response(JSON.stringify({ 
          status: 'ok', 
          environment: env.ENVIRONMENT,
          timestamp: new Date().toISOString()
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } else {
        response = new Response('Not Found', { status: 404 });
      }

      // Add CORS headers to response
      Object.entries(corsHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      return response;

    } catch (error) {
      console.error('Worker Error:', error);
      
      const errorResponse = new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });

      // Add CORS headers to error response
      Object.entries(corsHeaders).forEach(([key, value]) => {
        errorResponse.headers.set(key, value);
      });

      return errorResponse;
    }
  },
};
EOF
echo "   ✅ Created apps/service/src/index.ts"

# 创建文章处理器
cat > apps/service/src/handlers/articles.ts << 'EOF'
import type { Env } from '../index';

export async function handleArticles(request: Request, env: Env): Promise<Response> {
  const url = new URL(request.url);
  const pathname = url.pathname;

  if (request.method === 'GET') {
    if (pathname === '/api/articles') {
      // TODO: 实现文章列表 API
      return new Response(JSON.stringify({
        articles: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          total_pages: 0,
          has_next: false,
          has_prev: false
        }
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const articleMatch = pathname.match(/^\/api\/articles\/([^\/]+)$/);
    if (articleMatch) {
      const articleId = articleMatch[1];
      // TODO: 实现文章详情 API
      return new Response(JSON.stringify({
        id: articleId,
        title: 'Mock Article',
        description: 'This is a mock article for development',
        // ... other fields
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  return new Response('Not Found', { status: 404 });
}
EOF
echo "   ✅ Created apps/service/src/handlers/articles.ts"

# 创建共享类型文件
echo "📝 Creating shared types..."
cat > shared/types.ts << 'EOF'
// API 契约类型定义
export type Level = 'N5' | 'N4' | 'N3' | 'N2' | 'N1';

export interface Article {
  id: string;
  title: string;
  description: string;
  author: string;
  createdAt: string;
  tags: string[];
  category: string;
  difficulty: string;
  estimatedReadingTime: number;
  sentenceCount: number;
  jlptData?: JLPTData;
}

export interface JLPTData {
  [key in Level]: LevelData;
}

export interface LevelData {
  sentences: string[];
  grammar_features: string[];
  full_text: string;
  grammar_points: string[];
  word_count: number;
  description: string;
  audio_metadata?: AudioMetadata[];
}

export interface AudioMetadata {
  sentence_index: number;
  audio_path: string;
  duration: number;
  file_size: number;
  word_timings: WordTiming[];
}

export interface WordTiming {
  word: string;
  start_time: number;
  end_time: number;
  reading?: string;
  part_of_speech?: string;
  definition?: string;
}

// API 响应类型
export interface ArticleListResponse {
  articles: Article[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

export interface AudioResponse {
  audio_url: string;
  timing_url?: string;
  duration: number;
  file_size: number;
  expires_at: string;
}
EOF
echo "   ✅ Created shared/types.ts"

# 创建共享常量
cat > shared/constants.ts << 'EOF'
// 共享常量定义
export const API_ENDPOINTS = {
  ARTICLES: '/api/articles',
  AUDIO: '/api/audio',
  HEALTH: '/api/health',
} as const;

export const JLPT_LEVELS = ['N5', 'N4', 'N3', 'N2', 'N1'] as const;

export const CATEGORIES = [
  'daily-life',
  'school-life',
  'travel',
  'social-life',
  'culture',
  'work-life'
] as const;

export const DIFFICULTIES = [
  'beginner',
  'intermediate',
  'advanced'
] as const;

export const DEFAULT_PAGINATION = {
  page: 1,
  limit: 20,
  maxLimit: 100
} as const;
EOF
echo "   ✅ Created shared/constants.ts"

# 更新 mobile 的 tsconfig.json
cat > apps/mobile/tsconfig.json << 'EOF'
{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@shared/*": ["../../shared/*"]
    }
  },
  "include": ["src/**/*", "../../shared/**/*"],
  "exclude": ["node_modules"]
}
EOF
echo "   ✅ Updated apps/mobile/tsconfig.json"

# 创建开发脚本
echo "📜 Creating development scripts..."
cat > scripts/dev.sh << 'EOF'
#!/bin/bash

echo "🚀 Dokkai Development Environment"
echo ""
echo "选择要启动的服务:"
echo "1) Mobile App only"
echo "2) Service only" 
echo "3) Both (recommended)"
echo ""

read -p "请选择 (1-3): " choice

case $choice in
  1)
    echo "📱 Starting Mobile App..."
    cd apps/mobile && npm start
    ;;
  2)
    echo "🔧 Starting Service..."
    cd apps/service && npm run dev
    ;;
  3)
    echo "🚀 Starting both services..."
    echo ""
    echo "📱 Mobile App will be available at: http://localhost:8081"
    echo "🔧 Service will be available at: http://localhost:8787"
    echo ""
    echo "Press Ctrl+C to stop all services"
    
    # 启动 Service
    cd apps/service && npm run dev &
    SERVICE_PID=$!
    
    # 等待 Service 启动
    sleep 3
    
    # 启动 Mobile App
    cd ../mobile && npm start &
    MOBILE_PID=$!
    
    # 清理函数
    cleanup() {
      echo ""
      echo "🛑 Stopping all services..."
      kill $SERVICE_PID $MOBILE_PID 2>/dev/null
      exit 0
    }
    
    trap cleanup SIGINT SIGTERM
    wait
    ;;
  *)
    echo "❌ Invalid choice"
    exit 1
    ;;
esac
EOF

chmod +x scripts/dev.sh
echo "   ✅ Created scripts/dev.sh"

# 创建部署脚本
cat > scripts/deploy.sh << 'EOF'
#!/bin/bash

echo "🚀 Dokkai Deployment Script"
echo ""
echo "选择部署目标:"
echo "1) Mobile App (EAS Build)"
echo "2) Service (Cloudflare Worker)"
echo "3) Both"
echo ""

read -p "请选择 (1-3): " choice

deploy_mobile() {
  echo "📱 Deploying Mobile App..."
  cd apps/mobile
  
  echo "选择构建类型:"
  echo "1) Development Build"
  echo "2) Preview Build" 
  echo "3) Production Build"
  
  read -p "请选择 (1-3): " build_type
  
  case $build_type in
    1) eas build --profile development --platform all ;;
    2) eas build --profile preview --platform all ;;
    3) eas build --profile production --platform all ;;
    *) echo "❌ Invalid choice"; exit 1 ;;
  esac
}

deploy_service() {
  echo "🔧 Deploying Service..."
  cd apps/service
  
  echo "选择部署环境:"
  echo "1) Development"
  echo "2) Staging"
  echo "3) Production"
  
  read -p "请选择 (1-3): " env_type
  
  case $env_type in
    1) wrangler deploy --env development ;;
    2) wrangler deploy --env staging ;;
    3) wrangler deploy --env production ;;
    *) echo "❌ Invalid choice"; exit 1 ;;
  esac
}

case $choice in
  1) deploy_mobile ;;
  2) deploy_service ;;
  3) 
    deploy_service
    echo ""
    deploy_mobile
    ;;
  *) echo "❌ Invalid choice"; exit 1 ;;
esac

echo "✅ Deployment completed!"
EOF

chmod +x scripts/deploy.sh
echo "   ✅ Created scripts/deploy.sh"

# 更新根 package.json
echo "📝 Updating root package.json..."
cat > package.json << 'EOF'
{
  "name": "dokkai",
  "version": "1.0.0",
  "description": "Dokkai - Japanese Learning App",
  "private": true,
  "scripts": {
    "dev": "./scripts/dev.sh",
    "deploy": "./scripts/deploy.sh",
    "dev:mobile": "cd apps/mobile && npm start",
    "dev:service": "cd apps/service && npm run dev",
    "build:mobile": "cd apps/mobile && npm run build",
    "build:service": "cd apps/service && npm run build",
    "type-check": "cd apps/mobile && npm run type-check && cd ../service && npm run type-check",
    "setup": "cd apps/mobile && npm install && cd ../service && npm install"
  },
  "devDependencies": {
    "typescript": "^5.0.0"
  }
}
EOF
echo "   ✅ Updated root package.json"

echo ""
echo "🎉 Migration completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Install dependencies:"
echo "   npm run setup"
echo ""
echo "2. Update mobile app imports to use shared types:"
echo "   Replace relative imports with: import { ... } from '@shared/types'"
echo ""
echo "3. Start development environment:"
echo "   npm run dev"
echo ""
echo "4. Install Wrangler CLI (if not already installed):"
echo "   npm install -g wrangler"
echo ""
echo "5. Implement service functionality by migrating from api-server/"
echo ""
echo "⚠️  Manual tasks required:"
echo "- Edit apps/mobile/package.json to remove service-related dependencies"
echo "- Update import paths in mobile app to use @shared/* for shared types"
echo "- Implement service handlers using api-server as reference"
echo "- Test both mobile and service work correctly"
echo ""
echo "🔍 Verification commands:"
echo "- npm run type-check  # Check TypeScript in both apps"
echo "- npm run dev:service # Start service on http://localhost:8787"
echo "- npm run dev:mobile  # Start mobile app on http://localhost:8081"
EOF