#!/bin/bash

echo "🚀 Dokkai Development Environment"
echo ""
echo "选择要启动的服务:"
echo "1) Mobile App only"
echo "2) Service only"
echo "3) Both (recommended)"
echo ""

read -p "请选择 (1-3): " choice

case $choice in
  1)
    echo "📱 Starting Mobile App..."
    cd apps/mobile && npm i && npm start
    ;;
  2)
    echo "🔧 Starting Service..."
    cd apps/service && npm i && npm run dev
    ;;
  3)
    echo "🚀 Starting both services..."
    echo ""
    echo "📱 Mobile App will be available at: http://localhost:8081"
    echo "🔧 Service will be available at: http://localhost:8787"
    echo ""
    echo "Press Ctrl+C to stop all services"

    # 启动 Service
    cd apps/service && npm i && npm run dev &
    SERVICE_PID=$!

    # 等待 Service 启动
    sleep 3

    # 启动 Mobile App
    cd ../mobile && npm i && npm start &
    MOBILE_PID=$!

    # 清理函数
    cleanup() {
      echo ""
      echo "🛑 Stopping all services..."
      kill $SERVICE_PID $MOBILE_PID 2>/dev/null
      exit 0
    }

    trap cleanup SIGINT SIGTERM
    wait
    ;;
  *)
    echo "❌ Invalid choice"
    exit 1
    ;;
esac
