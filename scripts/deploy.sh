#!/bin/bash

echo "🚀 Dokkai Deployment Script"
echo ""
echo "选择部署目标:"
echo "1) Mobile App (EAS Build)"
echo "2) Service (Cloudflare Worker)"
echo "3) Both"
echo ""

read -p "请选择 (1-3): " choice

deploy_mobile() {
  echo "📱 Deploying Mobile App..."
  cd apps/mobile
  
  echo "选择构建类型:"
  echo "1) Development Build"
  echo "2) Preview Build" 
  echo "3) Production Build"
  
  read -p "请选择 (1-3): " build_type
  
  case $build_type in
    1) eas build --profile development --platform all ;;
    2) eas build --profile preview --platform all ;;
    3) eas build --profile production --platform all ;;
    *) echo "❌ Invalid choice"; exit 1 ;;
  esac
}

deploy_service() {
  echo "🔧 Deploying Service..."
  cd apps/service
  
  echo "选择部署环境:"
  echo "1) Development"
  echo "2) Staging"
  echo "3) Production"
  
  read -p "请选择 (1-3): " env_type
  
  case $env_type in
    1) wrangler deploy --env development ;;
    2) wrangler deploy --env staging ;;
    3) wrangler deploy --env production ;;
    *) echo "❌ Invalid choice"; exit 1 ;;
  esac
}

case $choice in
  1) deploy_mobile ;;
  2) deploy_service ;;
  3) 
    deploy_service
    echo ""
    deploy_mobile
    ;;
  *) echo "❌ Invalid choice"; exit 1 ;;
esac

echo "✅ Deployment completed!"
